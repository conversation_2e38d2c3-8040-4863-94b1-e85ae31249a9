/* pages/workorder/list/index.wxss */

/* 容器 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  overflow: hidden;
}



/* 筛选区域 */
.filter-section {
  position: fixed;
  top: 0; /* 直接从顶部开始 */
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 99;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  height: 155px; /* 增加高度以容纳类型标签栏 */
}

.status-tabs {
  display: flex;
  white-space: nowrap;
  padding: 8px 16px;
  border-bottom: 1px solid #eee;
}

.status-tab {
  display: inline-block;
  padding: 6px 12px;
  margin-right: 8px;
  font-size: 14px;
  color: #666666; /* 文本次色 */
  border-radius: 16px;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.status-tab.active {
  background-color: #FF8C00; /* 主品牌色 */
  color: #fff;
  border-color: #FF8C00;
}

/* 类型标签栏 */
.type-tabs {
  display: flex;
  white-space: nowrap;
  padding: 8px 16px;
  border-bottom: 1px solid #eee;
}

.type-tab {
  display: inline-block;
  padding: 6px 12px;
  margin-right: 8px;
  font-size: 14px;
  color: #666666; /* 文本次色 */
  border-radius: 16px;
  transition: all 0.3s;
  border: 1px solid transparent;
  background-color: #f8f9fa;
}

.type-tab.active {
  background-color: #1890ff; /* 使用蓝色区分类型筛选 */
  color: #fff;
  border-color: #1890ff;
}

.search-box {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background-color: #f5f7fa;
  margin: 8px 16px 12px;
  border-radius: 8px;
}

.search-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.search-input {
  flex: 1;
  height: 32px;
  font-size: 14px;
}

/* 工单列表 */
.order-list {
  margin-top: 155px; /* 调整为新的筛选区域高度 */
  padding: 8px 12px 0; /* 添加上下左右内边距，确保与搜索框有间距 */
  box-sizing: border-box;
  height: calc(100% - 320rpx);
}

/* 工单卡片 */
.order-card {
  position: relative;
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  width: calc(100% - 4px);
  box-sizing: border-box;
  overflow: hidden;
  border: 1px solid #E0E0E0; /* 边框色 */
}

.order-card.pinned {
  border: 2px solid #FF8C00; /* 主品牌色 */
  box-shadow: 0 4px 12px rgba(255, 140, 0, 0.15);
}

/* 原置顶标签样式（保留但不使用） */
.pin-tag {
  position: absolute;
  top: 0;
  right: 16px;
  background-color: #FF8C00; /* 主品牌色 */
  color: #fff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 0 0 8px 8px;
  display: none; /* 隐藏原来的置顶标签 */
}

/* 新的内联置顶标签样式 */
.pin-tag-inline {
  display: inline-block;
  background-color: #FF8C00; /* 主品牌色 */
  color: #fff;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
  vertical-align: middle;
  line-height: 1.2;
}

.order-header {
  display: flex;
  align-items: flex-start;
  width: 100%;
  overflow: hidden;
  position: relative;
}

/* 左侧类型标识区域 */
.type-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 16px;
  width: 60px;
}

.order-icon-container {
  width: 48px;
  height: 48px;
  background-color: #FFF0E0; /* 主色的浅色背景 */
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
  display: none; /* 隐藏图标容器 */
}

.order-icon {
  width: 28px;
  height: 28px;
}

.order-type-tag {
  font-size: 14px; /* 增大字体 */
  padding: 4px 12px; /* 增加内边距 */
  background-color: #FFF0E0; /* 主色的浅色背景 */
  color: #FF8C00; /* 主品牌色 */
  border-radius: 12px;
  white-space: nowrap;
  text-align: center;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 中间主要信息区域 */
.order-info {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  padding-right: 12px;
}

/* 标题容器，用于包含标题和置顶标签 */
.order-title-container {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px; /* 增加与下方信息的间距 */
}

.order-title {
  font-size: 17px;
  font-weight: 600; /* 加粗，提高标题的视觉重要性 */
  color: #333333; /* 文本主色 */
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  flex: 1; /* 让标题占据剩余空间 */
}

.order-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.order-id, .order-time {
  font-size: 12px;
  color: #999999; /* 文本辅助色 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 右侧状态区域 */
.order-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 80px;
  margin-right: 10px; /* 增加右侧边距，避免与图钉图标重叠 */
}

.status-tag {
  font-size: 12px;
  padding: 4px 10px; /* 增加内边距，提高可读性 */
  border-radius: 4px;
  margin-bottom: 6px; /* 增加间距 */
  white-space: nowrap;
  font-weight: 500; /* 加粗，提高视觉重要性 */
}

.status-pending {
  background-color: #FFF7E6; /* 警告色的浅色背景 */
  color: #FAAD14; /* 警告色 */
}

.status-processing {
  background-color: #FFF0E0; /* 主色的浅色背景 */
  color: #FF8C00; /* 主品牌色 */
}

.status-completed {
  background-color: #F6FFED; /* 成功色的浅色背景 */
  color: #52C41A; /* 成功色 */
}

.status-cancelled {
  background-color: #F5F5F5; /* 中性色背景 */
  color: #999999; /* 文本辅助色 */
}

.priority-tag {
  font-size: 12px;
  padding: 3px 8px; /* 稍微增加内边距 */
  border-radius: 4px;
  margin-bottom: 6px; /* 增加间距 */
  white-space: nowrap;
}

.priority-high {
  background-color: #FFF1F0; /* 错误色的浅色背景 */
  color: #F5222D; /* 错误色 */
  font-weight: 500; /* 高优先级加粗 */
}

.priority-medium {
  background-color: #FFF7E6; /* 警告色的浅色背景 */
  color: #FAAD14; /* 警告色 */
}

.priority-low {
  background-color: #F5F5F5; /* 中性色背景 */
  color: #999999; /* 文本辅助色 */
}

.pin-button {
  position: absolute;
  top: 0;
  right: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 0 12px 0 8px;
  transition: background-color 0.2s;
  background-color: transparent; /* 移除背景色 */
  z-index: 5;
}

.pin-button:hover {
  background-color: rgba(255, 140, 0, 0.1); /* 悬停时显示浅橙色背景 */
}

.pin-icon {
  width: 18px;
  height: 18px;
  transition: transform 0.3s ease;
}

.pin-button.active .pin-icon {
  transform: rotate(0deg);
}

.pin-button:not(.active) .pin-icon {
  transform: rotate(90deg);
}

.pin-indicator {
  display: none; /* 隐藏状态区域内的图钉指示器 */
  align-items: center;
  justify-content: center;
  margin-top: 6px;
}

.pin-indicator .pin-icon {
  width: 16px;
  height: 16px;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #eee;
  gap: 8px; /* 按钮之间的间距 */
}

.action-btn {
  font-size: 13px;
  padding: 6px 12px;
  border-radius: 16px; /* 胶囊形按钮 */
  font-weight: 500; /* 加粗文字 */
  transition: all 0.3s;
  border: none;
  line-height: 1.4;
  min-width: 0; /* 允许按钮宽度自适应 */
  max-width: fit-content; /* 按钮宽度自适应内容 */
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 查看详情按钮 */
.view-btn {
  background-color: #FFFFFF;
  color: #FF8C00; /* 主品牌色 */
  border: 1px solid #FF8C00;
}

.view-btn:hover {
  background-color: #FFF0E0; /* 主色的浅色背景 */
}

/* 取消按钮 */
.cancel-btn {
  background-color: #FFFFFF;
  color: #F5222D; /* 错误色 */
  border: 1px solid #F5222D;
}

.cancel-btn:hover {
  background-color: #FFF1F0; /* 错误色的浅色背景 */
}

/* 查看进度按钮 */
.progress-btn {
  background-color: #FF8C00; /* 主品牌色 */
  color: #FFFFFF; /* 白色文字 */
}

.progress-btn:hover {
  background-color: #E67A00; /* 主色的深色变体 */
}

/* 评价按钮 */
.evaluate-btn {
  background-color: #52C41A; /* 成功色 */
  color: #FFFFFF; /* 白色文字 */
}

.evaluate-btn:hover {
  background-color: #389E0D; /* 成功色的深色变体 */
}

/* 加载中提示 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 0;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #eee;
  border-top-color: #FF8C00; /* 主品牌色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999;
  margin-top: 8px;
}

/* 空状态提示 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 0;
}

.empty-icon {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #999;
  margin-bottom: 24px;
}

.create-btn {
  background-color: #FF8C00; /* 主品牌色 */
  color: #fff;
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 8px;
  transition: background-color 0.3s;
}

.create-btn:hover {
  background-color: #E67A00; /* 主色的深色变体 */
}

/* 分页指示器 */
.pagination {
  display: flex;
  justify-content: center;
  padding: 16px 0;
}

.page-info {
  font-size: 14px;
  color: #999;
}


/* 悬浮添加按钮 */
.add-btn {
  position: fixed;
  bottom: 100px;
  right: 20px;
  width: 56px;
  height: 56px;
  background-color: #FF8C00;
  border-radius: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(255, 140, 0, 0.3);
  z-index: 100;
}

.add-icon {
  width: 24px;
  height: 24px;
}