# 恢复API原始字段结构

## 问题描述

之前的代码重新定义了API返回的字段，导致与原始数据结构不一致，造成了字段混乱。

## 修复原则

**不要自定义字段，直接使用API返回的原始字段结构。**

## 真实数据结构

### 1. 发布列表数据结构

```javascript
{
  address: "吴江区江陵街道",
  amount: 9999,
  categoryCode: "digital_electronics",
  collect: 0,
  createTime: "2025-07-17 15:13:23",
  distance: null,
  examineNote: null,
  id: "35",
  isCollect: null,
  isLike: null,
  isMy: true,
  lat: 0,
  like: 0,
  lng: 0,
  media: "assets/37f3be7de46d416aa1487b26c54da85c.jpg",
  orderExpireTime: 199,
  points: 0,
  sellerAvatarUrl: "assets/a5ac649395614478a208eba2278a64e0.jpg",
  sold: 1,
  status: "list",
  stock: 8887,
  stuffDescribe: "豆腐干地方fd ",
  timeUnit: "hour",
  title: "豆腐干反对",
  type: "second_hand",
  updateTime: "2025-07-17 18:47:23",
  userId: "39",
  userName: "helloworld",
  views: 2
}
```

### 2. 订单列表数据结构

#### 我出售的订单
```javascript
{
  buyerDeleted: false,
  buyerId: "38",
  createTime: "2025-07-17 18:47:23",
  expireTime: null,
  id: "102",
  note: null,
  orderNo: "GN2025071718472301000",
  quantity: 1,
  sellerDeleted: false,
  sellerId: "39",
  status: "wait_complete",
  stuffSnapshot: "JSON字符串",
  totalAmount: 9999,
  unitAmount: 9999,
  updateTime: null
}
```

#### 我购买的订单
```javascript
{
  buyerDeleted: false,
  buyerId: "39",
  createTime: "2025-07-17 16:20:49",
  expireTime: null,
  id: "101",
  note: null,
  orderNo: "GN2025071716204901000",
  quantity: 1,
  sellerDeleted: false,
  sellerId: "38",
  status: "wait_complete",
  stuffSnapshot: "JSON字符串",
  totalAmount: 0,
  unitAmount: 0,
  updateTime: null
}
```

### 3. 收藏列表数据结构

```javascript
{
  address: "吴江区江陵街道",
  amount: 0,
  categoryCode: "house_elect",
  collect: 1,
  createTime: "2025-07-17 15:57:44",
  distance: null,
  examineNote: "商品上架",
  id: "36",
  isCollect: true,
  isLike: null,
  isMy: null,
  lat: 0,
  like: 0,
  lng: 0,
  media: "assets/8dffedbab101458c81d9e6ed72aaf400.jpg,assets/6287db88a4de4c57be35b1bd1416183c.jpg,assets/ebff44180fe746e5ad844eed0c604701.jpg",
  orderExpireTime: 1,
  points: 0,
  sellerAvatarUrl: "assets/3b10bb7bb0be47c4aa6af86e2a4a4a71.jpg",
  sold: 1,
  status: "list",
  stock: 998,
  stuffDescribe: "nnnnnnn",
  timeUnit: "hour",
  title: "啊哈哈哈",
  type: "free",
  updateTime: "2025-07-17 18:45:57",
  userId: "38",
  userName: "我查我自己",
  views: 20
}
```

## 修复方案

### 1. 发布列表数据处理

```javascript
// 修复前：重新定义字段
const goods = {
  id: item.id,
  title: item.title || '',
  stuffDescribe: item.stuffDescribe || '',
  // ... 重新定义所有字段
};

// 修复后：使用原始字段
const goods = {
  ...item, // 保留所有原始字段
  statusName: this.getGoodsStatus(item.status), // 只添加计算字段
  statusClass: this.getGoodsStatusClass(item.status),
  typeName: this.getGoodsTypeName(item.type),
  isFreeType: this.checkIsFreeType(item.type),
  categoryName: this.getGoodsCategoryName(item.categoryCode),
  createTime: item.createTime ? dateUtil.formatTime(new Date(item.createTime)) : item.createTime
};
```

### 2. 收藏列表数据处理

```javascript
// 修复前：重新映射字段
const favoriteItem = {
  id: item.id,
  goodsId: item.goodStuffId,
  title: item.goodStuffDescribe || '',
  stuffDescribe: item.goodStuffDescribe || '',
  // ... 重新映射所有字段
};

// 修复后：使用原始字段
const favoriteItem = {
  ...item, // 保留所有原始字段
  typeName: this.getGoodsTypeName(item.type),
  isFreeType: this.checkIsFreeType(item.type),
  createTime: item.createTime ? dateUtil.formatTime(new Date(item.createTime)) : item.createTime
};
```

### 3. 联系卖家逻辑修复

```javascript
// 修复前：使用错误的字段名
sellerId = item.sellerId || '';
sellerName = item.sellerName || item.memberName || '卖家';
goodsId = item.goodsId || item.id || '';

// 修复后：使用正确的原始字段
// 收藏数据处理
sellerId = item.userId || '';
sellerName = item.userName || '卖家';
goodsId = item.id || '';
goodsTitle = item.title || item.stuffDescribe || '';

// 图片处理也使用正确字段
if (item.media) {
  const images = item.media.split(',');
  goodsImage = images[0].trim();
}
```

## 字段对应关系

### 发布列表和收藏列表（直接返回商品信息）
- **卖家ID**: `userId`
- **卖家名称**: `userName`
- **卖家头像**: `sellerAvatarUrl`
- **商品ID**: `id`
- **商品标题**: `title`
- **商品描述**: `stuffDescribe`
- **商品图片**: `media`（逗号分隔）
- **商品价格**: `amount`

### 订单列表（需要解析stuffSnapshot）
- **卖家ID**: `stuffSnapshot.userId`
- **卖家名称**: `stuffSnapshot.userName`
- **商品ID**: `stuffSnapshot.id`
- **商品标题**: `stuffSnapshot.title`
- **商品描述**: `stuffSnapshot.stuffDescribe`
- **商品图片**: `stuffSnapshot.media`（逗号分隔）

## 修改的文件

1. **`profilePackage/pages/goods/my/my.js`**
   - 发布列表数据处理：使用 `...item` 保留原始字段
   - 收藏列表数据处理：使用 `...item` 保留原始字段
   - 联系卖家逻辑：使用正确的原始字段名

## 重要原则

1. **保持原始性**：直接使用API返回的字段，不重新定义
2. **最小修改**：只添加必要的计算字段（如statusName、typeName等）
3. **字段一致性**：确保前端使用的字段名与API文档一致
4. **避免映射**：不要将API字段映射为自定义字段名

## 验证方法

1. **控制台检查**：在浏览器控制台查看实际的API返回数据
2. **字段对比**：确保前端使用的字段名与API返回的字段名完全一致
3. **功能测试**：测试联系卖家、商品显示等功能是否正常

通过这次修复，确保了代码与API数据结构的完全一致，避免了字段混乱的问题。
