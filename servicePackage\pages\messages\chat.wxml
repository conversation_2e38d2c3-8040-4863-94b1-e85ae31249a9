<view class="chat-container">
  <!-- 连接状态显示 -->
  <view class="connection-status {{isConnected ? 'connected' : 'disconnected'}}">
    <text class="status-text">{{connectionStatus}}</text>
    <view class="status-indicator {{isConnected ? 'online' : 'offline'}}"></view>
  </view>

  <!-- 商品信息卡片 -->
  <view class="goods-card" wx:if="{{goodsInfo}}" bindtap="handleGoodsTap">
    <image class="goods-image" src="{{goodsInfo.image}}" mode="aspectFill"></image>
    <view class="goods-info">
      <view class="goods-title">{{goodsInfo.title}}</view>
      <view class="goods-price">¥{{goodsInfo.price}}</view>
    </view>
    <view class="goods-arrow">
      <image class="arrow-icon" src="/images/icons/arrow-right.svg"></image>
    </view>
  </view>

  <!-- 聊天消息列表 -->
  <scroll-view class="message-list" scroll-y="true" scroll-into-view="{{scrollIntoView}}" scroll-with-animation="{{!isInitialLoad}}" enable-flex="true">
    <view class="message-list-inner">
      <block wx:for="{{messages}}" wx:key="id">
        <!-- 对方发送的消息 -->
        <view class="message-item other" wx:if="{{item.senderId + '' !== userInfo.id + ''}}" id="msg{{index}}">
          <view class="avatar-container">
            <image class="avatar" src="{{targetAvatar}}" mode="aspectFill"></image>
          </view>
          <view class="message-content">
          
            <view class="message-bubble" wx:if="{{item.type === 'text'}}">
              {{item.content}}
            </view>
            <view class="message-bubble image" wx:elif="{{item.type === 'image'}}" bindtap="previewImage" data-url="{{apiUrl + item.content}}">
              <image class="message-image" src="{{apiUrl + item.content}}" mode="aspectFit"></image>
            </view>
            <view class="message-time">{{item.createTime}}</view>
          </view>
        </view>

        <!-- 自己发送的消息 -->
        <view class="message-item self" wx:else id="msg{{index}}">
          <view class="avatar-container">
            <image class="avatar" src="{{userInfo.avatarUrl?(apiUrl+userInfo.avatarUrl):'/images/default-avatar.svg'}}  " mode="aspectFill"></image>
          </view>
          <view class="message-content">
          
            <view class="message-bubble" wx:if="{{item.type === 'text'}}">
              {{item.content}}
            </view>
            <view class="message-bubble image" wx:elif="{{item.type === 'image'}}" bindtap="previewImage" data-url="{{apiUrl + item.content}}">
              <image class="message-image" src="{{apiUrl + item.content}}" mode="aspectFit"></image>
            </view>
            <view class="message-time">{{item.createTime}}</view>
          </view>
        </view>
      </block>

      <!-- 占位元素，确保滚动到底部 -->
      <view class="message-placeholder" id="msg{{messages.length}}"></view>
    </view>
  </scroll-view>

  <!-- 底部输入区域 -->
  <view class="input-area">
    <view class="input-actions">
      <view class="action-item" bindtap="sendImage">
        <image class="action-icon" src="/images/icons/image.svg"></image>
      </view>
    </view>
    <view class="input-box">
      <input class="message-input" value="{{inputContent}}" bindinput="handleInputChange" placeholder="请输入消息..." confirm-type="send" bindconfirm="sendMessage" />
    </view>
    <view class="send-btn {{inputContent ? 'active' : ''}}" bindtap="sendMessage">发送</view>
  </view>
</view>