const REQUEST = require('../utils/request.js')

//用户端实名认证 /users-api/v1/auth/real-name
function submitRealNameAuth(authData) {
  // authData 包含以下字段:
  // {
  //     "residentName": "string",
  //     "certificateType": "string",
  //     "idCardNumber": "string",
  //     "phone": "16030446442",
  //     "codeKey": "string",
  //     "code": "string",
  //     "photo": "string"
  // }
  return REQUEST.request('/users-api/v1/member/real-name', 'POST', authData, true);
}
//完善个人信息
function supplementUserInfo(params) {

  return REQUEST.request('/users-api/v1/member/supplement', 'POST', params, true);
}

//获取实名认证信息
function getUserInfo()
{
  return REQUEST.request('/users-api/v1/member/info', 'GET', {}, true);
}

//获取物业认证信息
function getProperTyInfo(communityId)
{
  
  return REQUEST.request('/users-api/v1/member/person/info?communityId='+communityId, 'GET', {}, true);
}

//注销用户
function logout()
{
  return REQUEST.request('/users-api/v1/member/logout', 'POST', {}, true);
}

//获取物业员工认证状态
function getPropertyStaffAuth() {
  return REQUEST.request('/users-api/v1/member/person/real-name', 'GET', {}, true);
}

//物业员工认证
function submitPropertyAuth(authData) {
  // authData 包含以下字段:
  // {
  //     "personName": "员工姓名",
  //     "certificateType": "证件类型nameEn",
  //     "idCard": "证件号码",
  //     "phone": "手机号码",
  //     "personNumber": "员工编号",
  //     "media": "图片上传后接口返回的图片文件地址",
  //     "orgId": "所选部门id",
  //     "positionId": "职位id"
  // }               
  return REQUEST.request('/users-api/v1/member/person/real-name', 'POST', authData, true);
}

//根据userId查询用户信息
function getUserInfoByUserId(userId) {
  return REQUEST.request('/users-api/v1/member/info/'+userId, 'GET', {}, true);
}


module.exports = {
  submitRealNameAuth,
  supplementUserInfo,
  getUserInfo,
  getProperTyInfo,
  logout,
  getPropertyStaffAuth,
  submitPropertyAuth,
  getUserInfoByUserId
}


