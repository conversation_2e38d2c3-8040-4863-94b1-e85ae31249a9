// profile.js
const util = require('@/utils/util.js')
const PointsUtil = require('@/utils/points.js')
const app = getApp()
const TabbarManager = require('@/utils/tabbar-manager.js')
const houseApi = require('@/api/houseApi.js')
const vehicleApi = require('@/api/vehicleApi.js')
const familyApi = require('@/api/familyApi.js')

Page({
  data: {
    apiUrl: wx.getStorageSync('apiUrl') + '/common-api/v1/file/',
    isAuthenticated: false,
    realName: '',

    avatarUrl: '/images/default-avatar.svg',

    houseCount: 0,
    vehicleCount: 0,
    familyCount: 0,
    tenantCount: 0,
    tenantFamilyCount: 0,
    points: 0,
    communityName: '',
    authBadgeText: '未认证',
    authBadgeClass: '',
    isPropertyStaff: false, // 是否为物业员工
    personAuthBadgeText: '未认证', // 物业员工认证状态文本
    personAuthBadgeClass: '', // 物业员工认证状态样式
    personStatusOptions: [], // 物业员工状态字典选项
    showAuthReminder: false,
    pendingUrl: '',
    darkMode: false
  },

  onLoad: function () {
    // 初始化页面数据
    this.loadDictionaries();
  },

  onShow: function () {

    console.log('my show')
    console.log('userInfo', wx.getStorageSync('userInfo'))
    console.log('propertyInfo', wx.getStorageSync('propertyInfo'))
    this.checkAuthStatus()
    this.loadAssetCounts()
    this.loadCommunityInfo()
    this.loadUserPoints()

    // 检查是否有积分变动事件
    if (app.globalData && app.globalData.pointsChangeEvent) {
      console.log('个人中心页面检测到积分变动事件:', app.globalData.pointsChangeEvent);
      // 重新加载用户积分
      this.loadUserPoints();
    }

    // 更新底部tabbar选中状态
    TabbarManager.setTabbarSelected(this, 4)
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    console.log('个人中心页面下拉刷新');

    // 刷新所有数据
    Promise.allSettled([
      this.checkAuthStatus(), // 使用首页的checkAuthStatus方法，会自动获取用户信息和物业信息
      this.loadAssetCounts(),
      this.loadCommunityInfo(),
      this.loadUserPoints()
    ]).then(() => {
      setTimeout(() => {
        wx.stopPullDownRefresh();
      }, 1500);
    }).catch(err => {
      console.error('下拉刷新失败:', err);
      setTimeout(() => {
        wx.stopPullDownRefresh();
      }, 1500);
    });
  },

  // 加载字典数据
  loadDictionaries: function () {
    try {
      // 获取物业员工状态字典
      const personStatusDict = util.getDictByNameEn('person_status');

      this.setData({
        personStatusOptions: personStatusDict && personStatusDict.length > 0 && personStatusDict[0].children ? personStatusDict[0].children : []
      });

      console.log('我的页面字典数据加载完成:', {
        personStatusOptions: this.data.personStatusOptions
      });
    } catch (error) {
      console.error('加载我的页面字典数据失败:', error);
      this.setData({
        personStatusOptions: []
      });
    }
  },

  // 加载资产数量
  loadAssetCounts: function () {
    // 检查是否满足加载条件：已登录 + 已选择小区 + 已认证
    const checkResult = util.checkLoginAndCommunity();
    const authResult = util.checkUserAuthenticated();

    if (!checkResult.canProceed || !authResult.isAuthenticated) {
      console.log('不满足加载条件，跳过资产数量加载');
      this.setData({
        houseCount: 0,
        vehicleCount: 0,
        familyCount: 0,
        tenantCount: 0,
        tenantFamilyCount:0
      });
      return;
    }

    // 满足条件时从API获取数据
    this.loadHouseData();
    this.loadVehicleData();
    this.loadFamilyData();
    this.loadTenantData();
  },

  // 加载车辆数据
  loadVehicleData: function () {
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.log('未选择小区，跳过车辆数据加载');
      return;
    }

    const params = {
      pageNum: 1,
      pageSize: 100,
      communityId: selectedCommunity.id
    };

    vehicleApi.getVehicleList(params)
      .then(res => {
        console.log('车辆数据：', res);
        if (res && res.list) {
          this.setData({
            vehicleCount: res.list.length
          });
        } else {
          this.setData({
            vehicleCount: 0
          });
        }
      })
      .catch(err => {
        console.error('获取车辆列表异常：', err);
        this.setData({
          vehicleCount: 0
        });
      });
  },


  // 加载房屋数据
  loadHouseData: function () {
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.log('未选择小区，跳过房屋数据加载');
      return;
    }

    const params = {
      pageNum: 1,
      pageSize: 100,
      communityId: selectedCommunity.id
    };

    houseApi.getHouseList(params)
      .then(res => {
        console.log('房屋数据：', res);
        if (res && res.list) {
          this.setData({
            houseCount: res.list.length
          });
        } else {
          this.setData({
            houseCount: 0
          });
        }
      })
      .catch(err => {
        console.error('获取房屋列表异常：', err);
        this.setData({
          houseCount: 0
        });
      });
  },

  // 加载家人数据
  loadFamilyData: function () {
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.log('未选择小区，跳家人数据加载');
      return;
    }

    const params = {
      pageNum: 1,
      pageSize: 100,
      communityId: selectedCommunity.id,
      residentType: "family"
    };

    familyApi.getFamilyList(params)
      .then(res => {
        console.log('家人数据：', res);
        if ( res && res.list) {
          this.setData({
            familyCount: res.list.length
          });
        } else {
          this.setData({
            familyCount: 0
          });
        }
      })
      .catch(err => {
        console.error('获取家人列表异常：', err);
        this.setData({
          familyCount: 0
        });
      });
  },

  // 加载租客数据
  loadTenantData: function () {
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.log('未选择小区，跳过租客数据加载');
      return;
    }

    const params = {
      pageNum: 1,
      pageSize: 100,
      communityId: selectedCommunity.id,
      residentType: "tenant"
    };

    familyApi.getFamilyList(params)
      .then(res => {
        console.log('租客数据：', res);
        if (res && res.list) {
          this.setData({
            tenantCount: res.list.length
          });
        } else {
          this.setData({
            tenantCount: 0
          });
        }
      })
      .catch(err => {
        console.error('获取租客列表异常：', err);
        this.setData({
          tenantCount: 0
        });
      });





  },


  // 加载小区信息
  loadCommunityInfo: function () {
    const communityInfo = wx.getStorageSync('selectedCommunity') || {}
    if (communityInfo.name) {
      this.setData({
        communityName: communityInfo.name
      })
    }
  },

  // 加载用户积分
  loadUserPoints: function () {
    // 检查是否已认证
    if (!this.data.isAuthenticated) {
      return;
    }

    // 使用积分工具获取用户积分
    PointsUtil.getUserPoints().then(points => {
      this.setData({
        points: points
      });
      console.log('个人中心页面加载积分:', points);
    }).catch(err => {
      console.error('获取用户积分失败:', err);
      // 设置默认积分为0
      this.setData({
        points: 0
      });
    });
  },

  // 检查认证状态（使用首页的方法，获取最新的用户信息和物业信息）
  checkAuthStatus: function () {
    return new Promise((resolve, reject) => {
      const userApi = require('@/api/userApi.js');

      userApi.getUserInfo().then(res => {
        if (res) {
          var userInfo = res;
          // 统一保存用户信息
          wx.setStorageSync('userInfo', userInfo);

          if (userInfo.role != 'tourist') {
            // 获取物业员工认证信息
            this.getPropertyStaffInfo().then(() => {
              // 更新UI显示
              this.updateAuthStatusDisplay();
              resolve();
            }).catch(err => {
              console.error('获取物业认证信息失败:', err);
              this.updateAuthStatusDisplay();
              resolve(); // 即使物业信息获取失败，也要继续
            });
          } else {
            // 游客状态，清除物业信息
            wx.removeStorageSync('propertyInfo');
            wx.setStorageSync('isPropertyStaff', false);
            this.updateAuthStatusDisplay();
            resolve();
          }
        } else {
          // 没有用户信息，清除所有认证信息
          wx.removeStorageSync('propertyInfo');
          wx.setStorageSync('isPropertyStaff', false);
          this.updateAuthStatusDisplay();
          resolve();
        }
      }).catch(err => {
        console.error('获取用户信息失败:', err);
        this.updateAuthStatusDisplay();
        reject(err);
      });
    });
  },

  // 获取物业员工认证信息
  getPropertyStaffInfo: function() {
    return new Promise((resolve, reject) => {
      var communityId = wx.getStorageSync('selectedCommunity')?.id;

      if (communityId) {
        const userApi = require('@/api/userApi.js');
        userApi.getProperTyInfo(communityId).then(res => {
          if (res) {
            console.log('物业员工认证信息获取成功:', res);
            // 统一保存用户信息
            wx.setStorageSync('propertyInfo', res);
            wx.setStorageSync('isPropertyStaff', true);

            // 保存新格式的物业认证信息，供权限检查使用
            const propertyAuth = {
              isAuthenticated: true,
              status: res.status || 'pending', // 如果没有status字段，默认为pending状态
              ...res // 包含其他所有字段
            };
            wx.setStorageSync('propertyAuth', propertyAuth);

            console.log('物业员工认证信息已保存:', propertyAuth);
          } else {
            wx.removeStorageSync('propertyInfo');
            wx.removeStorageSync('propertyAuth');
            wx.setStorageSync('isPropertyStaff', false);
          }
          resolve(res);
        }).catch(err => {
          console.error('获取物业认证信息失败:', err);
          wx.removeStorageSync('propertyInfo');
          wx.removeStorageSync('propertyAuth');
          wx.setStorageSync('isPropertyStaff', false);
          reject(err);
        });
      } else {
        console.log('没有选择小区，跳过物业员工认证信息获取');
        resolve();
      }
    });
  },

  // 更新认证状态显示
  updateAuthStatusDisplay: function() {
    const util = require('@/utils/util.js');
    const authResult = util.checkUserAuthenticated();
    const userInfo = authResult.userInfo;

    let realName = '';
    let avatarUrl = '/images/default-avatar.svg';

    if (userInfo) {
      realName = userInfo.realName || '';
      // 头像显示逻辑：显示时需要加上apiUrl前缀
      if (userInfo.avatarUrl) {
        avatarUrl = this.data.apiUrl + userInfo.avatarUrl;
      }
    }

    // 检查是否为物业员工
    const isPropertyStaff = wx.getStorageSync('isPropertyStaff') || false;
    const propertyInfo = wx.getStorageSync('propertyInfo') || {};

    // 设置住户认证状态文本和样式
    let authBadgeText = '未认证';
    let authBadgeClass = '';

    if (authResult.isAuthenticated) {
      authBadgeText = '已认证';
      authBadgeClass = 'verified';
    }

    // 设置物业员工认证状态文本和样式 - 使用person_status字典
    let personAuthBadgeText = '未认证';
    let personAuthBadgeClass = '';

    if (isPropertyStaff && propertyInfo.status) {
      // 使用person_status字典匹配状态
      const statusOption = this.data.personStatusOptions.find(option => option.nameEn === propertyInfo.status);

      if (statusOption) {
        // 直接使用字典的nameCn作为显示文本
        personAuthBadgeText = statusOption.nameCn;
        // 统一使用物业员工认证样式，不区分具体状态
        personAuthBadgeClass = 'property-verified';
      } else {
        // 如果字典中找不到对应状态，显示原始状态值
        personAuthBadgeText = propertyInfo.status || '未知状态';
        personAuthBadgeClass = 'property-verified';
      }
    } else if (isPropertyStaff) {
      // 有物业员工标记但没有状态信息
      personAuthBadgeText = '已认证';
      personAuthBadgeClass = 'property-verified';
    }

    this.setData({
      isAuthenticated: authResult.isAuthenticated,
      isPropertyStaff,
      realName,
      avatarUrl,
      authBadgeText,
      authBadgeClass,
      personAuthBadgeText,
      personAuthBadgeClass
    });
  },

  // 头像点击 - 跳转到完善个人信息页面
  changeAvatar: function () {
    wx.navigateTo({
      url: '/profilePackage/pages/profile/complete-info/complete-info'
    });
  },

  // 跳转到积分页面
  navigateToPoints: function () {
    // 检查用户是否已认证
    if (!this.data.isAuthenticated) {
      // 未认证，显示认证提醒
      this.showAuthReminder('/pages/points/points')
      return
    }

    // 已认证，直接跳转到积分页面
    wx.switchTab({
      url: '/pages/points/points'
    })
  },


  // 跳转到认证页面
  navigateToAuth: function () {
    const authResult = util.checkUserAuthenticated();

    if (authResult.isAuthenticated) {
      // 已认证用户：跳转到认证页面进行修改，隐藏物业员工认证选项
      wx.navigateTo({
        url: '/pages/auth/real-name/real-name?hasAuth=true&authType=resident&mode=edit'
      });
    } else {
      // 未认证用户：跳转到认证页面，隐藏物业员工认证选项，只显示住户认证
      wx.navigateTo({
        url: '/pages/auth/real-name/real-name?authType=resident&mode=auth'
      });
    }
  },

  // 普通页面跳转
  navigateToPage: function (e) {
    const url = e.currentTarget.dataset.url;

    // 检查是否是需要特殊处理的页面
    if (url.includes('/house/') || url.includes('/vehicle/') ||
      url.includes('/family/') || url.includes('/tenant/')) {
      // 资产管理相关页面需要检测认证状态
      const authResult = util.checkUserAuthenticated();
      const communityResult = util.checkCommunitySelected();

      if (!authResult.userInfo) {
        // 未登录
        wx.showModal({
          title: '需要认证',
          content: '请先认证后再使用此功能',
          showCancel: false,
          confirmText: '确定'
        });
        return;
      }

      if (!communityResult.isSelected) {
        // 未选择小区
        wx.showModal({
          title: '选择小区',
          content: '请先选择您所在的小区',
          confirmText: '去选择',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              wx.setStorageSync('redirectAfterCommunitySelect', url);
              wx.navigateTo({
                url: '/pages/community-select/community-select'
              });
            }
          }
        });
        return;
      }

      if (!authResult.isAuthenticated) {
        // 未认证：跳转到住户认证页面
        wx.showModal({
          title: '需要实名认证',
          content: '此功能需要完成住户实名认证后才能使用',
          confirmText: '去认证',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              wx.setStorageSync('redirectAfterAuth', url);
              wx.navigateTo({
                url: '/pages/auth/real-name/real-name?authType=resident&mode=auth'
              });
            }
          }
        });
        return;
      }

      // 所有检测通过，跳转到目标页面
      wx.navigateTo({
        url: url
      });
    } else {
      // 普通页面直接跳转
      wx.navigateTo({
        url: url
      });
    }
  },

  // 需要认证的页面跳转（服务记录相关）
  navigateWithAuth: function (e) {
    const url = e.currentTarget.dataset.url;

    // 检查是否是需要房产验证的页面
    const needsPropertyCheck = [
      '/servicePackage/pages/workorder/list/index', // 我的工单
      '/servicePackage/pages/payment/history/history', // 我的缴费
      '/profilePackage/pages/goods/my/my', // 我的好物
      '/profilePackage/pages/profile/my-activities/my-activities' // 我的活动
    ];

    if (needsPropertyCheck.includes(url)) {
      // 需要房产验证的页面，先检查房产
      this.checkPropertyAndNavigate(url);
    } else {
      // 其他服务记录功能需要检测：已登录 + 已选择小区
      util.checkStatusAndNavigate(url, {
        requireAuth: true,
        requireCommunity: true
      });
    }
  },

  // 检查房产并跳转
  checkPropertyAndNavigate: function(url) {
    // 首先检查基本条件：已登录 + 已选择小区 + 已认证
    const authResult = util.checkUserAuthenticated();
    const communityResult = util.checkCommunitySelected();

    if (!authResult.userInfo) {
      // 未登录
      wx.showModal({
        title: '需要认证',
        content: '请先认证后再使用此功能',
        showCancel: false,
        confirmText: '确定'
      });
      return;
    }

    if (!communityResult.isSelected) {
      // 未选择小区
      wx.showModal({
        title: '选择小区',
        content: '请先选择您所在的小区',
        confirmText: '去选择',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.setStorageSync('redirectAfterCommunitySelect', url);
            wx.navigateTo({
              url: '/pages/community-select/community-select'
            });
          }
        }
      });
      return;
    }

    if (!authResult.isAuthenticated) {
      // 未认证：跳转到住户认证页面
      wx.showModal({
        title: '需要实名认证',
        content: '此功能需要完成住户实名认证后才能使用',
        confirmText: '去认证',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.setStorageSync('redirectAfterAuth', url);
            wx.navigateTo({
              url: '/pages/auth/real-name/real-name?authType=resident&mode=auth'
            });
          }
        }
      });
      return;
    }

    // 基本条件满足，检查房产
    this.checkHouseProperty(url);
  },

  // 检查房产
  checkHouseProperty: function(url) {
    // 检查当前房产数量
    if (this.data.houseCount > 0) {
      // 有房产，直接跳转
      wx.navigateTo({
        url: url
      });
    } else {
      // 没有房产，显示添加房产提示
      wx.showModal({
        title: '添加房产',
        content: '使用此功能需要先添加房产信息，是否立即添加？',
        confirmText: '去添加',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 保存目标URL，添加房产后跳转
            wx.setStorageSync('redirectAfterAddHouse', url);
            wx.navigateTo({
              url: '/profilePackage/pages/profile/house/add/add'
            });
          }
        }
      });
    }
  },

  // 物业员工认证
  navigateToPropertyAuth: function () {

    if (!util.checkAuthentication()) {
      util.showAuthModal()
      return
    }
    const propertyResult = util.checkPropertyAuthenticated();

    if (propertyResult.isPropertyAuthenticated) {
      // 已认证用户：跳转到认证页面进行修改
      wx.navigateTo({
        url: '/pages/auth/real-name/real-name?hasAuth=true&authType=property&mode=edit'
      });
    } else {
      // 未认证用户：跳转到认证页面，只显示物业员工认证
      wx.navigateTo({
        url: '/pages/auth/real-name/real-name?authType=property&mode=auth'
      });
    }
  },

  // 显示认证提醒弹窗
  showAuthReminder: function (url) {
    this.setData({
      showAuthReminder: true,
      pendingUrl: url
    })
  },

  // 关闭认证提醒弹窗
  closeAuthReminder: function () {
    this.setData({
      showAuthReminder: false,
      pendingUrl: ''
    })
  },

  // 前往认证
  goToAuth: function () {
    this.setData({
      showAuthReminder: false
    })

    wx.navigateTo({
      url: '/pages/auth/real-name/real-name'
    })
  },

  // 意见反馈
  showFeedback: function () {
    wx.showModal({
      title: '意见反馈',
      content: '感谢您的反馈，请联系客服电话：400-123-4567',
      showCancel: false
    })
  },

  // 退出登录
  logout: function () {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除所有登录相关的存储
          wx.removeStorageSync('userInfo');
          wx.removeStorageSync('access_token');
          wx.removeStorageSync('refresh_token');
          wx.removeStorageSync('openid');
          wx.removeStorageSync('session_key');
          wx.removeStorageSync('memberDetail');
          wx.removeStorageSync('isPropertyStaff');

          // 清除物业认证相关信息
          wx.removeStorageSync('propertyAuth');
          wx.removeStorageSync('propertyInfo');

          // 清除用户房产信息
          wx.removeStorageSync('my_houses');
          wx.removeStorageSync('selectedHouse');

          // 清除车辆信息
          wx.removeStorageSync('my_vehicles');

          // 清除家属信息
          wx.removeStorageSync('family_members');

          // 清除租客信息
          wx.removeStorageSync('tenant_members');

          // 清除小区选择信息
          wx.removeStorageSync('selectedCommunity');

          // 清除认证状态
          wx.removeStorageSync('isAuthenticated');
          wx.removeStorageSync('realName');

          // 清除访客相关信息
          wx.removeStorageSync('visitors');
          wx.removeStorageSync('carNumberHistory');

          // 清除积分变动事件
          if (app.globalData) {
            app.globalData.pointsChangeEvent = null;
            app.globalData.isAuthenticated = false;
            app.globalData.isLoginInitializing = false;
            app.globalData.loginInitPromise = null;
          }

          // 更新页面状态
          this.setData({
            isAuthenticated: false,
            isPropertyStaff: false,
            realName: '',
            authBadgeText: '未认证',
            authBadgeClass: '',
            personAuthBadgeText: '未认证',
            personAuthBadgeClass: '',
            points: 0
          })

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  }
})
