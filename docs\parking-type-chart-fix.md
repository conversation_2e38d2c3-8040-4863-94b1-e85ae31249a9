# 车位类型分布图表字典匹配修复

## 问题描述

在数据统计页面的车位类型分布图表中，显示的是原始的字典值（如 `temporary_parking`、`fixed_parking`），而不是对应的中文名称。需要使用字典值 `parking_type` 来匹配显示正确的中文名称。

## 问题分析

### 修复前的问题

在 `loadParkingData` 方法中，车位类型分布数据直接使用了API返回的原始字典值：

```javascript
// 修复前：直接使用原始字典值
const parkingTypeDistribution = Object.keys(parkingTypeCount).map(type => ({
  name: type, // 这里直接使用了如 'temporary_parking' 的原始值
  value: parkingTypeCount[type]
}));
```

这导致图表中显示的是：
- `temporary_parking`
- `fixed_parking`

而不是用户友好的中文名称。

### 期望的效果

应该显示字典 `parking_type` 中对应的中文名称：
- `临时车位`
- `固定车位`

## 修复方案

### 1. 修改数据处理逻辑

在 `loadParkingData` 方法中，添加字典匹配逻辑：

```javascript
// 修复后：使用字典匹配
const parkingTypeDistribution = Object.keys(parkingTypeCount).map(type => {
  const typeName = this.getParkingTypeName(type);
  return {
    name: typeName, // 使用字典匹配后的中文名称
    value: parkingTypeCount[type]
  };
});
```

### 2. 添加字典匹配方法

新增 `getParkingTypeName` 方法来根据字典值获取中文名称：

```javascript
// 获取车位类型中文名称
getParkingTypeName: function(typeCode) {
  try {
    const util = require('../../../utils/util.js');
    const parkingTypes = util.getDictByNameEn('parking_type');
    
    if (parkingTypes && parkingTypes[0] && parkingTypes[0].children) {
      const typeItem = parkingTypes[0].children.find(item => item.nameEn === typeCode);
      if (typeItem) {
        return typeItem.nameCn;
      }
    }
    
    console.warn('未找到车位类型字典值:', typeCode);
    return typeCode; // 如果找不到字典值，返回原始值
  } catch (error) {
    console.error('获取车位类型名称失败:', error);
    return typeCode; // 出错时返回原始值
  }
}
```

## 字典数据结构

`parking_type` 字典的数据结构应该类似：

```javascript
{
  "nameEn": "parking_type",
  "nameCn": "车位类型",
  "children": [
    {
      "nameEn": "temporary_parking",
      "nameCn": "临时车位"
    },
    {
      "nameEn": "fixed_parking", 
      "nameCn": "固定车位"
    },
    {
      "nameEn": "visitor_parking",
      "nameCn": "访客车位"
    }
    // ... 其他车位类型
  ]
}
```

## 容错处理

### 1. 字典不存在的情况

如果 `parking_type` 字典不存在或数据结构不正确，方法会：
- 输出警告日志
- 返回原始的字典值作为显示名称
- 确保图表仍能正常显示

### 2. 字典值不匹配的情况

如果API返回的字典值在字典中找不到对应项，方法会：
- 输出警告日志，指明具体的未匹配值
- 返回原始值作为显示名称
- 不影响其他正确匹配的数据

### 3. 异常处理

如果在字典匹配过程中发生异常，方法会：
- 捕获并记录错误
- 返回原始值确保功能不中断
- 保证图表的正常渲染

## 测试验证

### 1. 正常情况测试

1. 确保 `parking_type` 字典已正确配置
2. 访问数据统计页面的车辆分类快照
3. 检查车位类型分布图表是否显示中文名称

### 2. 字典缺失测试

1. 临时移除或修改 `parking_type` 字典
2. 访问数据统计页面
3. 检查图表是否仍能显示（使用原始值）
4. 检查控制台是否有相应的警告信息

### 3. 数据为空测试

1. 确保在没有车位数据时图表能正确处理
2. 检查空数据情况下的显示效果

## 相关文件

### 1. 修改的文件

- **`propertyPackage/pages/property/statistics/statistics.js`**
  - 修改 `loadParkingData` 方法，添加字典匹配
  - 新增 `getParkingTypeName` 方法

### 2. 依赖的文件

- **`utils/util.js`** - 提供 `getDictByNameEn` 方法
- **字典配置** - 需要正确配置 `parking_type` 字典

## 其他类似问题

这个修复方案可以作为模板，应用到其他需要字典匹配的图表中：

### 1. 工单状态图表

可能需要使用 `work_order_status` 字典匹配

### 2. 房屋类型图表

可能需要使用相应的房屋类型字典匹配

### 3. 访客目的图表

可能需要使用访客相关的字典匹配

## 最佳实践

### 1. 统一的字典匹配方法

可以考虑创建一个通用的字典匹配工具方法：

```javascript
// 通用字典匹配方法
getDictDisplayName: function(dictName, code) {
  try {
    const util = require('../../../utils/util.js');
    const dictData = util.getDictByNameEn(dictName);
    
    if (dictData && dictData[0] && dictData[0].children) {
      const item = dictData[0].children.find(item => item.nameEn === code);
      if (item) {
        return item.nameCn;
      }
    }
    
    console.warn(`未找到字典值 ${dictName}:`, code);
    return code;
  } catch (error) {
    console.error(`获取字典显示名称失败 ${dictName}:`, error);
    return code;
  }
}
```

### 2. 数据处理标准化

在处理统计数据时，统一进行字典匹配：

```javascript
// 标准化的数据处理流程
const processStatisticsData = (rawData, dictName) => {
  return Object.keys(rawData).map(key => ({
    name: this.getDictDisplayName(dictName, key),
    value: rawData[key]
  }));
};
```

## 预期效果

修复后，车位类型分布图表应该显示：
- 图表标题：车位类型分布
- 图例和数据标签：显示中文名称（如"临时车位"、"固定车位"）
- 用户体验：更加友好和直观的数据展示

这个修复确保了数据统计页面的车位类型分布图表能够正确显示中文名称，提升了用户体验。
