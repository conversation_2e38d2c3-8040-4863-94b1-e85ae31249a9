# 好物发布前置条件检测功能

## 功能概述

为好物页面的发布按钮添加了完整的前置条件检测，确保用户在发布好物前满足所有必要条件：
1. 用户已认证
2. 已选择小区
3. 已添加房产信息
4. 已设置默认房屋

## 实现方案

### 1. 检测流程

#### 1.1 用户认证检测
```javascript
if (!util.checkAuthentication()) {
  console.log('用户未认证，显示认证弹窗');
  util.showAuthModal();
  return;
}
```

#### 1.2 小区选择检测
```javascript
const selectedCommunity = wx.getStorageSync('selectedCommunity');
if (!selectedCommunity || !selectedCommunity.id) {
  // 显示选择小区弹窗
  wx.showModal({
    title: '选择小区',
    content: '发布好物需要先选择您所在的小区',
    confirmText: '去选择',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        // 保存发布页面URL，选择小区后跳转
        wx.setStorageSync('redirectAfterCommunitySelect', '/profilePackage/pages/goods/publish/publish');
        wx.navigateTo({
          url: '/pages/community-select/community-select'
        });
      }
    }
  });
  return;
}
```

#### 1.3 房产信息检测
```javascript
const params = { 
  pageNum: 1,
  pageSize: 100,
  communityId: selectedCommunity.id
};

houseApi.getHouseList(params)
  .then(res => {
    if (!res || !res.list || res.list.length === 0) {
      // 没有房产信息，显示添加房产弹窗
      this.showAddHouseModal();
      return;
    }

    // 检查是否有默认房屋
    const defaultHouse = res.list.find(house => house.isDefault === true);
    if (!defaultHouse) {
      // 没有默认房屋，显示设置默认房屋弹窗
      this.showSetDefaultHouseModal(res.list);
      return;
    }

    // 所有条件都满足，跳转到发布页面
    wx.navigateTo({
      url: '/profilePackage/pages/goods/publish/publish'
    });
  })
```

### 2. 核心方法

#### 2.1 主入口方法
```javascript
navigateToPublish: function() {
  console.log('点击发布按钮，开始检测条件...');
  
  // 第一步：检查是否已认证
  if (!util.checkAuthentication()) {
    util.showAuthModal();
    return;
  }

  // 第二步：检查是否已选择小区
  const selectedCommunity = wx.getStorageSync('selectedCommunity');
  if (!selectedCommunity || !selectedCommunity.id) {
    // 显示选择小区弹窗
    return;
  }

  // 第三步：检查房产信息
  this.checkHouseAndNavigate();
}
```

#### 2.2 房产检测方法
```javascript
checkHouseAndNavigate: function() {
  wx.showLoading({
    title: '检查房产信息...',
    mask: true
  });

  const selectedCommunity = wx.getStorageSync('selectedCommunity');
  const params = { 
    pageNum: 1,
    pageSize: 100,
    communityId: selectedCommunity.id
  };

  houseApi.getHouseList(params)
    .then(res => {
      wx.hideLoading();
      
      if (!res || !res.list || res.list.length === 0) {
        this.showAddHouseModal();
        return;
      }

      const defaultHouse = res.list.find(house => house.isDefault === true);
      if (!defaultHouse) {
        this.showSetDefaultHouseModal(res.list);
        return;
      }

      // 跳转到发布页面
      wx.navigateTo({
        url: '/profilePackage/pages/goods/publish/publish'
      });
    })
    .catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '获取房产信息失败',
        icon: 'none'
      });
    });
}
```

#### 2.3 添加房产弹窗
```javascript
showAddHouseModal: function() {
  wx.showModal({
    title: '需要房产信息',
    content: '发布好物需要先添加房产信息，是否立即添加？',
    confirmText: '去添加',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        // 保存发布页面URL，添加房产后跳转
        wx.setStorageSync('redirectAfterAddHouse', '/profilePackage/pages/goods/publish/publish');
        wx.navigateTo({
          url: '/profilePackage/pages/profile/house/add/add'
        });
      }
    }
  });
}
```

#### 2.4 设置默认房屋弹窗
```javascript
showSetDefaultHouseModal: function(houseList) {
  wx.showModal({
    title: '设置默认房屋',
    content: '发布好物需要设置一个默认房屋，是否前往设置？',
    confirmText: '去设置',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        // 保存发布页面URL，设置默认房屋后跳转
        wx.setStorageSync('redirectAfterSetDefault', '/profilePackage/pages/goods/publish/publish');
        wx.navigateTo({
          url: '/profilePackage/pages/profile/house/house'
        });
      }
    }
  });
}
```

## 用户体验流程

### 场景1：完整条件用户
1. 用户点击发布按钮
2. 系统检测：已认证 ✓ 已选择小区 ✓ 有房产 ✓ 有默认房屋 ✓
3. 直接跳转到好物发布页面

### 场景2：未认证用户
1. 用户点击发布按钮
2. 系统检测：未认证 ✗
3. 显示认证弹窗，引导用户完成认证

### 场景3：未选择小区用户
1. 用户点击发布按钮
2. 系统检测：已认证 ✓ 未选择小区 ✗
3. 显示选择小区弹窗
4. 用户选择小区后自动跳转到发布页面

### 场景4：无房产用户
1. 用户点击发布按钮
2. 系统检测：已认证 ✓ 已选择小区 ✓ 无房产 ✗
3. 显示添加房产弹窗
4. 用户添加房产后自动跳转到发布页面

### 场景5：无默认房屋用户
1. 用户点击发布按钮
2. 系统检测：已认证 ✓ 已选择小区 ✓ 有房产 ✓ 无默认房屋 ✗
3. 显示设置默认房屋弹窗
4. 用户设置默认房屋后自动跳转到发布页面

## 技术要点

### 1. API调用
- 使用 `houseApi.getHouseList()` 获取用户房产列表
- 参数包含 `communityId` 确保获取当前小区的房产
- 设置 `pageSize: 100` 确保获取所有房产

### 2. 默认房屋检测
- 使用 `Array.find()` 方法查找 `isDefault: true` 的房产
- 确保用户有明确的默认房屋设置

### 3. 跳转逻辑
- 使用本地存储保存目标页面URL
- 支持多级跳转后回到发布页面
- 存储键值：
  - `redirectAfterCommunitySelect`：选择小区后跳转
  - `redirectAfterAddHouse`：添加房产后跳转
  - `redirectAfterSetDefault`：设置默认房屋后跳转

### 4. 用户反馈
- 显示加载提示："检查房产信息..."
- 提供清晰的错误提示和操作指引
- 使用模态弹窗确认用户意图

## 依赖关系

### API依赖
- `houseApi.getHouseList()` - 获取房产列表
- `util.checkAuthentication()` - 检查用户认证状态
- `util.showAuthModal()` - 显示认证弹窗

### 页面依赖
- `/pages/community-select/community-select` - 小区选择页面
- `/profilePackage/pages/profile/house/add/add` - 添加房产页面
- `/profilePackage/pages/profile/house/house` - 房产管理页面
- `/profilePackage/pages/goods/publish/publish` - 好物发布页面

## 修改文件

- `pages/goods/goods.js` - 添加完整的检测逻辑

## 注意事项

1. **房产数据结构**：确保 `isDefault` 字段正确返回布尔值
2. **错误处理**：网络请求失败时提供友好的错误提示
3. **用户体验**：每个检测步骤都有明确的提示和引导
4. **数据一致性**：确保房产列表数据与其他页面保持一致
