# 物业员工认证信息刷新修复

## 问题描述

在首页返回时，只获取了用户信息，没有获取物业员工认证信息。原因是刷新方法中使用了错误的API接口。

## 问题分析

### 1. 接口使用错误

**错误的接口调用**：
```javascript
// ❌ 错误：使用了不存在的方法
userApi.getPropertyAuth()

// ❌ 错误：使用了错误的方法
userApi.getPropertyStaffAuth() // 这个方法是获取认证状态，不是获取认证信息
```

**正确的接口调用**：
```javascript
// ✅ 正确：使用与初始化相同的接口
userApi.getProperTyInfo(communityId)
```

### 2. 接口功能区别

根据`api/userApi.js`中的定义：

```javascript
// 获取物业认证信息（需要communityId参数）
function getProperTyInfo(communityId) {
  return REQUEST.request('/users-api/v1/member/person/info?communityId='+communityId, 'GET', {}, true);
}

// 获取物业员工认证状态（不需要参数，但功能不同）
function getPropertyStaffAuth() {
  return REQUEST.request('/users-api/v1/member/person/real-name', 'GET', {}, true);
}
```

- **`getProperTyInfo(communityId)`**：获取完整的物业员工信息，包括认证状态、权限等
- **`getPropertyStaffAuth()`**：只获取认证状态，不包含完整信息

## 修复方案

### 1. 统一接口使用

修改`refreshPropertyAuthInfo`方法，使其与初始化逻辑保持一致：

```javascript
// 刷新员工认证信息
refreshPropertyAuthInfo: function() {
  return new Promise((resolve, reject) => {
    console.log('🔄 刷新员工认证信息...');
    
    // 检查是否为游客模式
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || userInfo.role === 'tourist') {
      console.log('👤 游客模式，跳过员工认证检查');
      resolve();
      return;
    }
    
    // 获取小区ID
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    const communityId = selectedCommunity && selectedCommunity.id;
    
    if (!communityId) {
      console.log('❌ 没有小区ID，跳过员工认证检查');
      resolve();
      return;
    }
    
    // 获取员工认证信息（使用与初始化相同的接口）
    console.log('📡 调用getProperTyInfo接口，communityId:', communityId);
    userApi.getProperTyInfo(communityId)
      .then(res => {
        console.log('✅ 员工认证信息获取成功:', res);
        
        if (res) {
          // 保存员工认证信息（与初始化逻辑保持一致）
          wx.setStorageSync('propertyInfo', res);
          wx.setStorageSync('isPropertyStaff', true);

          // 保存新格式的物业认证信息，供权限检查使用
          const propertyAuth = {
            isAuthenticated: true,
            status: res.status || 'pending',
            ...res
          };
          wx.setStorageSync('propertyAuth', propertyAuth);
          
          // 更新页面状态
          const isPropertyStaff = res.status === 'active';
          this.setData({
            isPropertyStaff: isPropertyStaff
          });
          
          console.log('✅ 员工认证信息更新完成, isPropertyStaff:', isPropertyStaff);
          console.log('✅ 认证状态:', res.status);
        } else {
          // 清除物业认证信息
          wx.removeStorageSync('propertyInfo');
          wx.removeStorageSync('propertyAuth');
          wx.setStorageSync('isPropertyStaff', false);
          console.log('⚠️ 用户不是物业员工');
        }
        
        resolve(res);
      })
      .catch(error => {
        console.error('❌ 获取员工认证信息失败:', error);
        // 清除物业认证信息
        wx.removeStorageSync('propertyInfo');
        wx.removeStorageSync('propertyAuth');
        wx.setStorageSync('isPropertyStaff', false);
        resolve();
      });
  });
}
```

### 2. 数据存储一致性

确保刷新时的数据存储与初始化时保持一致：

```javascript
// 存储物业员工信息
wx.setStorageSync('propertyInfo', res);
wx.setStorageSync('isPropertyStaff', true);

// 存储新格式的物业认证信息
const propertyAuth = {
  isAuthenticated: true,
  status: res.status || 'pending',
  ...res
};
wx.setStorageSync('propertyAuth', propertyAuth);
```

### 3. 错误处理一致性

确保错误处理逻辑与初始化时保持一致：

```javascript
.catch(error => {
  console.error('❌ 获取员工认证信息失败:', error);
  // 清除物业认证信息
  wx.removeStorageSync('propertyInfo');
  wx.removeStorageSync('propertyAuth');
  wx.setStorageSync('isPropertyStaff', false);
  resolve(); // 不阻断流程
});
```

## 物业认证相关的API接口

### 1. 获取物业员工信息

```javascript
// 接口：/users-api/v1/member/person/info?communityId={communityId}
// 方法：userApi.getProperTyInfo(communityId)
// 用途：获取完整的物业员工信息，包括认证状态、权限等
// 参数：需要传递communityId
```

### 2. 获取物业员工认证状态

```javascript
// 接口：/users-api/v1/member/person/real-name
// 方法：userApi.getPropertyStaffAuth()
// 用途：只获取认证状态，不包含完整信息
// 参数：不需要参数
```

### 3. 提交物业员工认证

```javascript
// 接口：/users-api/v1/member/person/real-name
// 方法：userApi.submitPropertyAuth(authData)
// 用途：提交物业员工认证信息
// 参数：认证数据对象
```

## 使用场景区分

### 1. 初始化和刷新场景

使用`getProperTyInfo(communityId)`：
- 首页初始化时
- 从其他页面返回首页时
- 小区切换后重新检查时

### 2. 认证状态查询场景

使用`getPropertyStaffAuth()`：
- 在认证页面查询当前认证状态
- 检查认证进度时

### 3. 认证提交场景

使用`submitPropertyAuth(authData)`：
- 用户提交物业员工认证信息时

## 调试信息

修复后的调试信息更加详细：

```javascript
console.log('🔄 刷新员工认证信息...');
console.log('👤 游客模式，跳过员工认证检查');
console.log('❌ 没有小区ID，跳过员工认证检查');
console.log('📡 调用getProperTyInfo接口，communityId:', communityId);
console.log('✅ 员工认证信息获取成功:', res);
console.log('✅ 员工认证信息更新完成, isPropertyStaff:', isPropertyStaff);
console.log('✅ 认证状态:', res.status);
console.log('⚠️ 用户不是物业员工');
console.log('❌ 获取员工认证信息失败:', error);
```

## 测试验证

### 1. 物业员工用户测试

1. 使用已认证的物业员工账号登录
2. 从其他页面返回首页
3. 检查控制台是否有物业认证信息刷新的日志
4. 检查页面状态是否正确更新

### 2. 普通用户测试

1. 使用普通用户账号登录
2. 从其他页面返回首页
3. 检查是否正确跳过物业认证检查

### 3. 游客模式测试

1. 使用游客模式
2. 从其他页面返回首页
3. 检查是否正确跳过物业认证检查

### 4. 未选择小区测试

1. 清除小区选择
2. 从其他页面返回首页
3. 检查是否正确跳过物业认证检查

## 修改的文件

1. **`pages/index/index.js`**
   - 修复`refreshPropertyAuthInfo`方法
   - 使用正确的API接口`getProperTyInfo(communityId)`
   - 统一数据存储逻辑
   - 增强调试信息

## 预期效果

修复后，从其他页面返回首页时应该能够：

1. **正确获取物业员工信息** - 使用正确的API接口
2. **更新认证状态** - 及时反映后台审核结果
3. **保持数据一致性** - 与初始化逻辑完全一致
4. **提供详细日志** - 便于调试和问题排查

现在物业员工认证信息应该能够正确刷新了！
