# 小区选择持久化保存修复

## 问题描述

小区选择是用户的重要偏好设置，应该持久保存，不应该因为用户注销、退出登录等操作而被清除。但是在多个地方的清除逻辑中，都包含了清除小区选择的代码。

## 修复原则

**小区选择应该持久保存，除非用户主动重新选择，否则不应该被任何操作清除。**

## 修复的文件和位置

### 1. utils/request.js - clearUserInfo函数

**修复前**：
```javascript
// 清除小区选择信息
wx.removeStorageSync('selectedCommunity');
```

**修复后**：
```javascript
// 保留小区选择信息，不清除
// wx.removeStorageSync('selectedCommunity'); // 小区选择应该持久保存
```

### 2. pages/auth/real-name/real-name.js - 注销逻辑

#### 2.1 注销成功后的清除逻辑

**修复前**：
```javascript
// 清除小区选择信息
wx.removeStorageSync('selectedCommunity');
```

**修复后**：
```javascript
// 保留小区选择信息，不清除
// wx.removeStorageSync('selectedCommunity'); // 小区选择应该持久保存
```

#### 2.2 clearAllLocalData函数

**修复前**：
```javascript
// 清除小区选择信息
wx.removeStorageSync('selectedCommunity');
```

**修复后**：
```javascript
// 保留小区选择信息，不清除
// wx.removeStorageSync('selectedCommunity'); // 小区选择应该持久保存
```

### 3. pages/profile/profile.js - 退出登录逻辑

**修复前**：
```javascript
// 清除小区选择信息
wx.removeStorageSync('selectedCommunity');
```

**修复后**：
```javascript
// 保留小区选择信息，不清除
// wx.removeStorageSync('selectedCommunity'); // 小区选择应该持久保存
```

## 小区选择的使用场景

### 1. 小区选择的重要性

- **用户偏好**：小区选择是用户的基本偏好设置
- **业务依赖**：大部分功能都依赖于小区ID进行数据查询
- **用户体验**：避免用户重复选择小区，提升使用体验

### 2. 小区选择的存储结构

```javascript
// 存储格式
const selectedCommunity = {
  id: "123",
  communityName: "某某小区",
  name: "某某小区", // 兼容字段
  // ... 其他小区信息
};

// 存储到本地
wx.setStorageSync('selectedCommunity', selectedCommunity);
```

### 3. 小区选择的获取逻辑

```javascript
// 获取小区选择
const selectedCommunity = wx.getStorageSync('selectedCommunity');

// 兼容处理
let communityName = '';
if (selectedCommunity) {
  if (typeof selectedCommunity === 'object' && selectedCommunity.communityName) {
    communityName = selectedCommunity.communityName;
  } else if (typeof selectedCommunity === 'object' && selectedCommunity.name) {
    communityName = selectedCommunity.name;
  } else if (typeof selectedCommunity === 'string') {
    communityName = selectedCommunity;
  }
}
```

## 应该清除小区选择的场景

### 1. 用户主动重新选择

```javascript
// 在小区选择页面，用户选择新的小区
selectCommunity: function (e) {
  const community = e.currentTarget.dataset.community;
  
  // 保存新的小区选择
  wx.setStorageSync('selectedCommunity', community);
  
  wx.navigateBack();
}
```

### 2. 小区信息失效时

```javascript
// 检查小区选择是否有效
const checkCommunityValid = function() {
  const selectedCommunity = wx.getStorageSync('selectedCommunity');
  
  if (!selectedCommunity || !selectedCommunity.id) {
    // 小区信息无效，清除并重新选择
    wx.removeStorageSync('selectedCommunity');
    // 跳转到小区选择页面
    wx.navigateTo({
      url: '/pages/community-select/community-select'
    });
  }
};
```

## 不应该清除小区选择的场景

### 1. 用户注销

- **原因**：注销只是清除用户身份信息，不应该影响小区偏好
- **场景**：用户可能在同一个小区重新注册或登录

### 2. 退出登录

- **原因**：退出登录只是临时操作，用户可能很快重新登录
- **场景**：用户可能在同一个小区使用不同账号

### 3. Token过期或刷新

- **原因**：这是技术层面的操作，不应该影响用户偏好
- **场景**：自动token刷新不应该影响用户体验

### 4. 应用重启或更新

- **原因**：应用层面的操作不应该清除用户偏好
- **场景**：用户期望重启应用后仍然记住小区选择

## 相关API请求的处理

### 1. 需要小区ID的API

大部分API请求都需要在请求头中添加小区ID：

```javascript
// 在request.js中自动添加小区ID
const selectedCommunity = wx.getStorageSync('selectedCommunity');
if (selectedCommunity && selectedCommunity.id) {
  headers['communityId'] = selectedCommunity.id;
}
```

### 2. 小区选择检查

在需要小区信息的页面，应该检查小区选择状态：

```javascript
// 检查小区选择
checkCommunitySelection: function() {
  const selectedCommunity = wx.getStorageSync('selectedCommunity');
  
  if (!selectedCommunity || !selectedCommunity.id) {
    // 跳转到小区选择页面
    wx.navigateTo({
      url: '/pages/community-select/community-select'
    });
    return false;
  }
  
  return true;
}
```

## 测试验证

### 1. 注销测试

1. 选择一个小区
2. 进行用户注销操作
3. 重新登录
4. 检查小区选择是否仍然存在

### 2. 退出登录测试

1. 选择一个小区
2. 退出登录
3. 重新登录
4. 检查小区选择是否仍然存在

### 3. 应用重启测试

1. 选择一个小区
2. 关闭小程序
3. 重新打开小程序
4. 检查小区选择是否仍然存在

## 预期效果

修复后，小区选择应该具有以下特性：

1. **持久性**：不会因为注销、退出登录等操作被清除
2. **一致性**：在整个应用中保持一致的小区选择状态
3. **用户友好**：避免用户重复选择小区，提升使用体验
4. **业务连续性**：确保依赖小区ID的功能正常运行

## 注意事项

1. **数据迁移**：如果用户已经因为之前的逻辑丢失了小区选择，需要重新选择
2. **兼容性**：保持对旧版本小区选择数据格式的兼容
3. **错误处理**：当小区信息无效时，提供友好的重新选择流程
4. **性能考虑**：小区选择检查应该高效，避免影响应用性能

## 修改的文件总结

1. **`utils/request.js`** - 移除clearUserInfo中的小区清除逻辑
2. **`pages/auth/real-name/real-name.js`** - 移除注销和clearAllLocalData中的小区清除逻辑
3. **`pages/profile/profile.js`** - 移除退出登录中的小区清除逻辑

通过这些修复，确保了小区选择的持久性，提升了用户体验。
