<view class="visitor-container">
  

  <!-- 表单内容区域 -->
  <scroll-view scroll-y class="visitor-form-container">
    <!-- 批量邀请说明 -->
    <view class="batch-invite-intro">
      <view class="batch-invite-title">
        <image src="/images/icons/batch.svg" class="batch-invite-icon" />
        <text>批量邀请访客</text>
      </view>
      <view class="batch-invite-desc">
        <text>您可以一次性邀请多位访客，每位访客将收到单独的访客凭证。</text>
      </view>
    </view>

    <!-- 公共信息区域 -->
    <view class="batch-invite-common-info">
      <view class="batch-invite-section-title">公共信息</view>

      <!-- 来访时间 -->
      <view class="visitor-form-item">
        <text class="visitor-form-label">来访时间<text class="visitor-required">*</text></text>
        <view class="visitor-datetime-picker" bindtap="showDateTimePicker">
          <text>{{formData.visitDate}} {{formData.visitTime}}</text>
          <image src="/images/icons/calendar.svg" class="visitor-picker-icon" />
        </view>
        <view class="visitor-error-tip" wx:if="{{errors.visitDateTime}}">{{errors.visitDateTime}}</view>
      </view>

      <!-- 滞留时长 -->
      <view class="visitor-form-item">
        <text class="visitor-form-label">滞留时长<text class="visitor-required">*</text></text>
        <view class="visitor-duration-picker" bindtap="showDurationPicker">
          <text>{{formData.duration}}小时</text>
          <image src="/images/icons/clock.svg" class="visitor-picker-icon" />
        </view>
        <view class="visitor-error-tip" wx:if="{{errors.duration}}">{{errors.duration}}</view>
      </view>

      <!-- 来访目的 -->
      <view class="visitor-form-item">
        <text class="visitor-form-label">来访目的<text class="visitor-required">*</text></text>
        <view class="visitor-purpose-picker" bindtap="showPurposePicker">
          <text>{{formData.purpose || '请选择来访目的'}}</text>
          <image src="/images/icons/arrow-down.svg" class="visitor-picker-icon" />
        </view>
        <view class="visitor-error-tip" wx:if="{{errors.purpose}}">{{errors.purpose}}</view>
      </view>

      <!-- 访客类型选择 -->
      <view class="visitor-form-item">
        <text class="visitor-form-label">访客类型<text class="visitor-required">*</text></text>
        <view class="visitor-type-selector">
          <view class="visitor-type-option {{visitorType === 'person' ? 'active' : ''}}"
                bindtap="setVisitorType" data-type="person">
            <image src="/images/icons/person.svg" class="visitor-type-icon" />
            <text>人员访客</text>
          </view>
          <view class="visitor-type-option {{visitorType === 'car' ? 'active' : ''}}"
                bindtap="setVisitorType" data-type="car">
            <image src="/images/icons/car.svg" class="visitor-type-icon" />
            <text>车辆访客</text>
          </view>
        </view>
      </view>

      <!-- 备注信息 -->
      <view class="visitor-form-item">
        <text class="visitor-form-label">备注信息</text>
        <textarea name="remarks" class="visitor-form-textarea" placeholder="请输入备注信息（选填）"
                  value="{{formData.remarks}}" bindinput="inputRemarks" />
      </view>
    </view>

    <!-- 访客列表区域 -->
    <view class="batch-invite-visitors">
      <view class="batch-invite-section-title">
        <text>访客列表</text>
        <text class="batch-invite-count">({{visitors.length}})</text>
      </view>

      <!-- 访客列表 -->
      <view class="batch-visitor-list">
        <view class="batch-visitor-item" wx:for="{{visitors}}" wx:key="index">
          <view class="batch-visitor-info">
            <view class="batch-visitor-name">{{item.name}}</view>
            <view class="batch-visitor-phone">{{item.phone}}</view>
            <view class="batch-visitor-car" wx:if="{{visitorType === 'car' && item.carNumber}}">
              <text>车牌: {{item.carNumber}}</text>
            </view>
          </view>
          <view class="batch-visitor-actions">
            <view class="batch-visitor-edit" bindtap="editVisitor" data-index="{{index}}">
              <image src="/images/icons/edit.svg" class="batch-visitor-action-icon" />
            </view>
            <view class="batch-visitor-delete" bindtap="removeVisitor" data-index="{{index}}">
              <image src="/images/icons/delete.svg" class="batch-visitor-action-icon" />
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="batch-visitor-empty" wx:if="{{visitors.length === 0}}">
          <image src="/images/empty-state.png" class="empty-image" />
          <text>暂无访客，请添加访客信息</text>
        </view>
      </view>

      <!-- 添加访客按钮区域 -->
      <view class="add-visitor-buttons">
        <!-- 手动添加访客按钮 -->
        <view class="add-visitor-btn" bindtap="showAddVisitorModal">
          <image src="/images/icons/add.svg" class="add-visitor-icon" />
          <text>添加访客</text>
        </view>

        <!-- 选择常用访客按钮 -->
        <view class="add-visitor-btn frequent-visitor-btn" bindtap="showFrequentVisitorsModal">
          <image src="/images/icons/star-fill.svg" class="add-visitor-icon" />
          <text>常用访客</text>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 底部提交按钮 -->
  <view class="visitor-submit-btn-container">
    <button class="visitor-submit-btn" bindtap="submitBatchInvite" disabled="{{visitors.length === 0}}">
      生成{{visitors.length}}位访客凭证
    </button>
  </view>
</view>

<!-- 添加访客弹窗 -->
<view class="visitor-picker-modal {{showAddVisitorModal ? 'show' : ''}}">
  <view class="visitor-modal-mask" bindtap="hideAddVisitorModal"></view>
  <view class="visitor-picker-content">
    <view class="visitor-picker-header">
      <text class="visitor-picker-title">{{editingIndex !== -1 ? '编辑访客' : '添加访客'}}</text>
      <view class="visitor-picker-close" bindtap="hideAddVisitorModal">取消</view>
    </view>
    <view class="visitor-picker-body">
      <!-- 访客姓名 -->
      <view class="visitor-form-item">
        <text class="visitor-form-label">访客姓名<text class="visitor-required">*</text></text>
        <input class="visitor-form-input" placeholder="请输入访客姓名"
               value="{{currentVisitor.name}}" bindinput="inputCurrentVisitorName" />
        <view class="visitor-error-tip" wx:if="{{currentVisitorErrors.name}}">{{currentVisitorErrors.name}}</view>
      </view>

      <!-- 访客手机号 -->
      <view class="visitor-form-item">
        <text class="visitor-form-label">手机号码<text class="visitor-required">*</text></text>
        <view class="visitor-phone-input-container">
          <input class="visitor-form-input" placeholder="请输入手机号码"
                 type="number" maxlength="11" value="{{currentVisitor.phone}}"
                 bindinput="inputCurrentVisitorPhone" />
          <view class="visitor-paste-btn" bindtap="pastePhoneNumber">粘贴</view>
        </view>
        <view class="visitor-error-tip" wx:if="{{currentVisitorErrors.phone}}">{{currentVisitorErrors.phone}}</view>
      </view>

      <!-- 车牌号码 -->
      <view class="visitor-form-item" wx:if="{{visitorType === 'car'}}">
        <text class="visitor-form-label">车牌号码<text class="visitor-required">*</text></text>
        <view class="visitor-car-number-input-container">
          <input class="visitor-form-input" placeholder="请输入车牌号码"
                 value="{{currentVisitor.carNumber}}" bindinput="inputCurrentVisitorCarNumber" />
          <view class="visitor-history-btn" bindtap="showCarNumberHistory">
            <image src="/images/icons/history.svg" class="visitor-history-icon" />
          </view>
        </view>
        <view class="visitor-error-tip" wx:if="{{currentVisitorErrors.carNumber}}">{{currentVisitorErrors.carNumber}}</view>
      </view>
    </view>
    <view class="visitor-picker-footer">
      <button class="visitor-picker-confirm-btn" bindtap="confirmAddVisitor">确定</button>
    </view>
  </view>
</view>

<!-- 日期时间选择器弹窗 -->
<view class="visitor-picker-modal {{showDateTimePicker ? 'show' : ''}}">
  <view class="visitor-modal-mask" bindtap="hideDateTimePicker"></view>
  <view class="visitor-picker-content">
    <view class="visitor-picker-header">
      <text class="visitor-picker-title">选择来访时间</text>
      <view class="visitor-picker-close" bindtap="hideDateTimePicker">取消</view>
    </view>
    <view class="visitor-picker-body">
      <picker-view indicator-style="height: 50px;" style="width: 100%; height: 250px;"
                   value="{{dateTimePickerValue}}" bindchange="onDateTimePickerChange">
        <picker-view-column>
          <view wx:for="{{dateArray}}" wx:key="index" class="visitor-picker-item">{{item.label}}</view>
        </picker-view-column>
        <picker-view-column>
          <view wx:for="{{hourArray}}" wx:key="index" class="visitor-picker-item">{{item}}时</view>
        </picker-view-column>
        <picker-view-column>
          <view wx:for="{{minuteArray}}" wx:key="index" class="visitor-picker-item">{{item}}分</view>
        </picker-view-column>
      </picker-view>
    </view>
    <view class="visitor-picker-footer">
      <button class="visitor-picker-confirm-btn" bindtap="confirmDateTime">确定</button>
    </view>
  </view>
</view>

<!-- 滞留时长选择器弹窗 -->
<view class="visitor-picker-modal {{showDurationPicker ? 'show' : ''}}">
  <view class="visitor-modal-mask" bindtap="hideDurationPicker"></view>
  <view class="visitor-picker-content">
    <view class="visitor-picker-header">
      <text class="visitor-picker-title">选择滞留时长</text>
      <view class="visitor-picker-close" bindtap="hideDurationPicker">取消</view>
    </view>
    <view class="visitor-picker-body">
      <view class="visitor-duration-quick-select">
        <view wx:for="{{durationOptions}}" wx:key="index"
              class="visitor-duration-option {{formData.duration === item ? 'active' : ''}}"
              bindtap="selectDuration" data-duration="{{item}}">
          {{item}}小时
        </view>
      </view>
    </view>
    <view class="visitor-picker-footer">
      <button class="visitor-picker-confirm-btn" bindtap="confirmDuration">确定</button>
    </view>
  </view>
</view>

<!-- 来访目的选择器弹窗 -->
<view class="visitor-picker-modal {{showPurposePicker ? 'show' : ''}}">
  <view class="visitor-modal-mask" bindtap="hidePurposePicker"></view>
  <view class="visitor-picker-content">
    <view class="visitor-picker-header">
      <text class="visitor-picker-title">选择来访目的</text>
      <view class="visitor-picker-close" bindtap="hidePurposePicker">取消</view>
    </view>
    <view class="visitor-picker-body" style="padding: 0;">
      <view class="visitor-purpose-options">
        <view wx:for="{{purposeOptions}}" wx:key="index"
              class="visitor-purpose-option"
              bindtap="selectPurpose" data-purpose="{{item}}">
          {{item}}
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 车牌号历史记录弹窗 -->
<view class="visitor-picker-modal {{showCarNumberHistory ? 'show' : ''}}">
  <view class="visitor-modal-mask" bindtap="hideCarNumberHistory"></view>
  <view class="visitor-picker-content">
    <view class="visitor-picker-header">
      <text class="visitor-picker-title">车牌号历史记录</text>
      <view class="visitor-picker-close" bindtap="hideCarNumberHistory">取消</view>
    </view>
    <view class="visitor-picker-body">
      <view class="visitor-car-number-history">
        <view wx:if="{{carNumberHistory.length === 0}}" style="text-align: center; padding: 16px;">
          暂无历史记录
        </view>
        <view wx:for="{{carNumberHistory}}" wx:key="index"
              class="visitor-car-number-history-item"
              bindtap="selectCarNumber" data-car-number="{{item}}">
          {{item}}
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 批量邀请结果弹窗 -->
<view class="visitor-picker-modal {{showResultModal ? 'show' : ''}}">
  <view class="visitor-modal-mask" bindtap="hideResultModal"></view>
  <view class="visitor-picker-content">
    <view class="visitor-picker-header">
      <text class="visitor-picker-title">邀请结果</text>
      <view class="visitor-picker-close" bindtap="hideResultModal">关闭</view>
    </view>
    <view class="visitor-picker-body">
      <view class="batch-result-summary">
        <view class="batch-result-icon {{batchResult.success ? 'success' : 'error'}}">
          <image src="{{batchResult.success ? '/images/icons/success.svg' : '/images/icons/error.svg'}}" />
        </view>
        <view class="batch-result-text">
          <text wx:if="{{batchResult.success}}">成功添加 {{batchResult.successCount}}/{{batchResult.totalCount}} 位访客</text>
          <text wx:else>添加失败</text>
        </view>
      </view>

      <view class="batch-result-list" wx:if="{{batchResult.results.length > 0}}">
        <view class="batch-result-item" wx:for="{{batchResult.results}}" wx:key="index">
          <view class="batch-result-item-info">
            <view class="batch-result-item-header">
              <view class="batch-result-item-name">{{item.visitor.visitorName}}</view>
              <view class="batch-result-item-status {{item.success ? 'success' : 'error'}}">
                {{item.success ? '成功' : '失败'}}
              </view>
            </view>
            <view class="batch-result-item-error" wx:if="{{!item.success}}">
              <text>{{item.error}}</text>
            </view>
          </view>
          <view class="batch-result-item-action" wx:if="{{item.success}}">
            <button class="batch-result-view-btn" bindtap="viewVisitorCredential" data-id="{{item.data.id || item.data}}">查看凭证</button>
          </view>
        </view>
      </view>
    </view>
    <view class="visitor-picker-footer">
      <button class="visitor-picker-confirm-btn" bindtap="navigateToVisitorList">查看我的访客</button>
    </view>
  </view>
</view>

<!-- 常用访客选择弹窗 -->
<view class="visitor-picker-modal {{showFrequentVisitorsModal ? 'show' : ''}}">
  <view class="visitor-modal-mask" bindtap="hideFrequentVisitorsModal"></view>
  <view class="visitor-picker-content frequent-visitors-content">
    <view class="visitor-picker-header">
      <text class="visitor-picker-title">选择常用访客</text>
      <view class="visitor-picker-close" bindtap="hideFrequentVisitorsModal">取消</view>
    </view>
    <view class="visitor-picker-body">
      <!-- 全选/取消全选 -->
      <view class="frequent-visitors-select-all">
        <view class="frequent-visitors-checkbox {{selectAllFrequentVisitors ? 'checked' : ''}}"
              bindtap="toggleSelectAllFrequentVisitors">
          <view class="frequent-visitors-check-mark" wx:if="{{selectAllFrequentVisitors}}"></view>
        </view>
        <text class="frequent-visitors-select-all-text">全选</text>
        <text class="frequent-visitors-count">(已选{{selectedFrequentVisitors.length}}位)</text>
      </view>

      <!-- 常用访客列表 -->
      <view class="frequent-visitors-list">
        <view wx:if="{{frequentVisitors.length === 0}}" class="frequent-visitors-empty">
          <text>暂无常用访客</text>
        </view>
        <view wx:for="{{frequentVisitors}}" wx:key="id"
              class="frequent-visitor-item {{item.selected ? 'selected' : ''}}">
          <view class="frequent-visitor-checkbox {{item.selected ? 'checked' : ''}}"
                bindtap="toggleFrequentVisitor" data-id="{{item.id}}">
            <view class="frequent-visitor-check-mark" wx:if="{{item.selected}}"></view>
          </view>
          <view class="frequent-visitor-avatar">{{item.visitorName[0]}}</view>
          <view class="frequent-visitor-info">
            <view class="frequent-visitor-name">{{item.visitorName}}</view>
            <view class="frequent-visitor-phone">{{item.phone}}</view>
            <view class="frequent-visitor-car" wx:if="{{item.vehicleNumber}}">
              <text>车牌: {{item.vehicleNumber}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="visitor-picker-footer">
      <view class="visitor-picker-cancel-btn" bindtap="hideFrequentVisitorsModal">取消</view>
      <view class="visitor-picker-confirm-btn" bindtap="confirmSelectFrequentVisitors"
              disabled="{{selectedFrequentVisitors.length === 0}}">
        确定({{selectedFrequentVisitors.length}})
      </view>
    </view>
  </view>
</view>
