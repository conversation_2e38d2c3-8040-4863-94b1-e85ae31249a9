# 访客功能修复总结

## 5. 访客凭证页作废按钮修复

### 问题描述
访客凭证页作废按钮需要调用`visitorsApi.visitorCancel(id)`接口，并在对话框中添加关闭X号。

### 修复内容

#### 5.1 JavaScript修改 (`servicePackage/pages/visitor/credential/index.js`)

**添加数据字段：**
```javascript
data: {
  showCancelModal: false, // 是否显示作废确认弹窗
}
```

**修改作废功能：**
```javascript
// 显示作废确认弹窗
showCancelConfirm: function () {
  this.setData({
    showCancelModal: true
  });
},

// 隐藏作废确认弹窗
hideCancelModal: function () {
  this.setData({
    showCancelModal: false
  });
},

// 作废凭证
cancelCredential: function () {
  const visitorId = this.data.visitorData.id;
  
  wx.showLoading({
    title: '作废中...',
    mask: true
  });

  // 调用作废接口
  visitorsApi.visitorCancel(visitorId)
    .then(res => {
      wx.hideLoading();
      this.hideCancelModal();
      
      wx.showToast({
        title: '凭证已作废',
        icon: 'success'
      });

      // 获取最新的访客凭证数据
      this.getVisitorData();
    })
    .catch(error => {
      wx.hideLoading();
      console.error('作废凭证失败:', error);
      
      wx.showToast({
        title: error.message || '作废失败，请重试',
        icon: 'none'
      });
    });
}
```

#### 5.2 WXML修改 (`servicePackage/pages/visitor/credential/index.wxml`)

**添加作废确认弹窗：**
```xml
<!-- 作废确认弹窗 -->
<view class="visitor-modal-overlay {{showCancelModal ? 'show' : ''}}" bindtap="hideCancelModal">
  <view class="visitor-modal" catchtap="">
    <view class="visitor-modal-header">
      <text class="visitor-modal-title">作废凭证</text>
      <view class="visitor-modal-close" bindtap="hideCancelModal">
        <text>×</text>
      </view>
    </view>
    <view class="visitor-modal-body">
      <view class="visitor-modal-content">
        <text>确定要作废此访客凭证吗？</text>
        <text class="warning-text">作废后将无法恢复。</text>
      </view>
    </view>
    <view class="visitor-modal-footer">
      <button class="visitor-modal-btn visitor-modal-cancel-btn" bindtap="hideCancelModal">取消</button>
      <button class="visitor-modal-btn visitor-modal-confirm-btn danger" bindtap="cancelCredential">确定作废</button>
    </view>
  </view>
</view>
```

## 6. 访客凭证页延期按钮修复

### 问题描述
访客凭证页延期按钮需要实现与我的访客列表页相同的延期功能，调用API接口而不是本地更新。

### 修复内容

#### 6.1 JavaScript修改 (`servicePackage/pages/visitor/credential/index.js`)

**添加延期方法（与访客列表页相同）：**
```javascript
// 延长访客时间（与我的访客列表页相同的实现）
extendVisitor: function (e) {
  const hours = parseInt(e.currentTarget.dataset.hours);
  const { visitorData } = this.data;

  if (!visitorData || !visitorData.id) {
    wx.showToast({
      title: '访客信息不存在',
      icon: 'none'
    });
    return;
  }

  // 准备API参数
  const params = {
    id: visitorData.id,
    stayDuration: hours,
    timeUnit: "hours",
    visitTime: visitorData.originalVisitTime || visitorData.visitTime
  };

  console.log('延期访客参数：', params);

  // 显示加载状态
  wx.showLoading({
    title: '延期中...',
    mask: true
  });

  // 调用延期访客接口
  visitorsApi.editVisitTime(params)
    .then(res => {
      console.log('延期访客结果：', res);

      wx.hideLoading();

      // 延期成功
      this.hideExtendModal();

      wx.showToast({
        title: `已延长${hours}小时`,
        icon: 'success'
      });

      // 获取最新的访客凭证数据
      this.getVisitorData();
    })
    .catch(err => {
      console.error('延期访客失败：', err);

      wx.hideLoading();

      wx.showToast({
        title: err.message || '延期失败，请重试',
        icon: 'none'
      });
    });
}
```

#### 6.2 WXML修改 (`servicePackage/pages/visitor/credential/index.wxml`)

**修改延期选项：**
```xml
<view class="extend-option" bindtap="extendVisitor" data-hours="1">
  <text class="extend-option-value">延长1小时</text>
</view>
<view class="extend-option" bindtap="extendVisitor" data-hours="2">
  <text class="extend-option-value">延长2小时</text>
</view>
<view class="extend-option" bindtap="extendVisitor" data-hours="4">
  <text class="extend-option-value">延长4小时</text>
</view>
```

## 7. 访客凭证页立即生效按钮修复

### 问题描述
访客凭证页立即生效按钮需要调用编辑访客接口，将到访时间改成当前时间。

### 修复内容

#### 7.1 JavaScript修改 (`servicePackage/pages/visitor/credential/index.js`)

**修改立即生效方法：**
```javascript
// 立即生效
activateNow: function () {
  const { visitorData } = this.data;

  if (!visitorData || !visitorData.id) {
    wx.showToast({
      title: '访客信息不存在',
      icon: 'none'
    });
    return;
  }

  wx.showLoading({
    title: '处理中...',
    mask: true
  });

  // 获取当前时间，格式化为ISO字符串
  const now = new Date();
  const currentTime = now.toISOString();

  // 准备编辑访客的参数
  const params = {
    id: visitorData.id,
    visitTime: currentTime // 将到访时间改为当前时间
  };

  console.log('立即生效参数：', params);

  // 调用编辑访客接口
  visitorsApi.editVisitor(params)
    .then(res => {
      console.log('立即生效结果：', res);

      wx.hideLoading();

      wx.showToast({
        title: '已立即生效',
        icon: 'success'
      });

      // 获取最新的访客凭证数据
      this.getVisitorData();
    })
    .catch(error => {
      wx.hideLoading();
      console.error('立即生效失败:', error);

      wx.showToast({
        title: error.message || '操作失败，请重试',
        icon: 'none'
      });
    });
}
```

## 8. 批量邀请访客跳转问题修复

### 问题描述
批量邀请访客成功后的邀请结果弹窗中，点击查看凭证跳转到访客凭证页面时提示请检查请求参数，id显示undefined。

### 修复内容

#### 8.1 JavaScript修改 (`servicePackage/pages/visitor/batch-invite/index.js`)

**添加ID验证：**
```javascript
// 查看访客凭证
viewVisitorCredential: function(e) {
  const id = e.currentTarget.dataset.id;
  
  console.log('查看凭证，访客ID:', id);

  // 检查ID是否有效
  if (!id || id === 'undefined') {
    console.error('访客ID无效:', id);
    wx.showToast({
      title: '访客信息异常，无法查看凭证',
      icon: 'none'
    });
    return;
  }

  // 隐藏结果弹窗
  this.setData({
    showResultModal: false
  });

  // 跳转到访客凭证页面
  wx.navigateTo({
    url: '/servicePackage/pages/visitor/credential/index?id=' + id
  });
}
```

#### 8.2 WXML修改 (`servicePackage/pages/visitor/batch-invite/index.wxml`)

**修改数据绑定：**
```xml
<view class="batch-result-item-action" wx:if="{{item.success}}">
  <button class="batch-result-view-btn" bindtap="viewVisitorCredential" data-id="{{item.data.id || item.data}}">查看凭证</button>
</view>
```

## 通用改进：接口调用后刷新数据

### 统一的数据刷新机制

所有按钮操作（作废、延期、立即生效）成功后都会调用`this.getVisitorData()`来获取最新的访客凭证数据，确保页面显示的信息是最新的。

```javascript
// 统一的成功处理模式
.then(res => {
  wx.hideLoading();
  
  wx.showToast({
    title: '操作成功',
    icon: 'success'
  });

  // 获取最新的访客凭证数据
  this.getVisitorData();
})
```

## 修改的文件总结

1. **`servicePackage/pages/visitor/credential/index.js`**
   - 修复作废按钮调用API接口
   - 修复延期按钮使用API接口
   - 修复立即生效按钮调用编辑接口
   - 添加统一的数据刷新机制

2. **`servicePackage/pages/visitor/credential/index.wxml`**
   - 添加作废确认弹窗（带关闭X号）
   - 修改延期选项直接调用API方法

3. **`servicePackage/pages/visitor/batch-invite/index.js`**
   - 添加访客ID验证
   - 改进错误处理

4. **`servicePackage/pages/visitor/batch-invite/index.wxml`**
   - 修复数据绑定问题

## 测试验证

### 1. 作废功能测试
- 点击作废按钮显示确认弹窗
- 弹窗包含关闭X号
- 确认作废后调用API接口
- 成功后刷新访客数据

### 2. 延期功能测试
- 点击延期按钮显示选项
- 选择延期时长后调用API接口
- 成功后刷新访客数据

### 3. 立即生效功能测试
- 点击立即生效按钮调用编辑接口
- 将到访时间设为当前时间
- 成功后刷新访客数据

### 4. 批量邀请跳转测试
- 批量邀请成功后点击查看凭证
- 验证访客ID是否有效
- 正确跳转到访客凭证页面

## 预期效果

修复后，访客凭证页面的所有操作都将：
1. **调用正确的API接口** - 不再使用本地数据更新
2. **及时刷新数据** - 操作成功后获取最新状态
3. **提供良好的用户体验** - 加载状态、错误处理、成功提示
4. **数据一致性** - 确保页面显示与后台数据同步
