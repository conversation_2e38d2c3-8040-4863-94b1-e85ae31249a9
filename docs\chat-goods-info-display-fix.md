# 聊天页面商品信息显示修复

## 问题描述

1. **商品信息显示不一致**：从好物详情页跳转到聊天页面可以看到顶部商品信息，但从我的订单列表和收藏列表跳转时没有显示
2. **收藏列表联系卖家失败**：我的收藏列表点击联系卖家提示"卖家信息不完整"

## 修复方案

### 1. 统一商品信息传递

确保所有跳转到聊天页面的场景都传递商品信息参数：
- `goodsId`: 商品ID
- `goodsTitle`: 商品标题
- `goodsImage`: 商品图片

### 2. 修复收藏列表卖家信息获取

在收藏列表数据处理中，正确获取卖家userId：

```javascript
// profilePackage/pages/goods/my/my.js
const favoriteItem = {
  // ... 其他字段
  // 添加卖家信息字段，尝试多个可能的字段名
  sellerId: item.goodStuffMemberId || item.memberId || item.userId || item.goodStuffUserId || item.createBy || '',
  sellerName: item.goodStuffMemberName || item.memberName || item.userName || '用户',
};
```

### 3. 优化联系卖家逻辑

修改contactSeller方法，支持订单和收藏两种数据结构：

```javascript
contactSeller: function (e) {
  const item = e.currentTarget.dataset.order; // 订单或收藏数据
  
  let sellerId = '';
  let sellerName = '卖家';
  let goodsId = '';
  let goodsTitle = '';
  let goodsImage = '';

  // 判断是订单数据还是收藏数据
  if (item.stuffSnapshot || item.sellerId || item.orderNo) {
    // 订单数据处理
    if (item.stuffSnapshot && item.stuffSnapshot.userId) {
      // 从交易快照中获取卖家信息
      sellerId = item.stuffSnapshot.userId;
      sellerName = item.stuffSnapshot.userName || '卖家';
    } else {
      // 兜底：从订单数据中获取
      sellerId = item.sellerId || item.userId || '';
      sellerName = item.sellerName || item.userName || '卖家';
    }
    
    goodsId = item.goodsId || item.id || '';
    goodsTitle = item.stuffDescribe || item.title || '';
    goodsImage = item.image || item.media || '';
  } else {
    // 收藏数据处理
    sellerId = item.sellerId || '';
    sellerName = item.sellerName || item.memberName || '卖家';
    goodsId = item.goodsId || item.id || '';
    goodsTitle = item.stuffDescribe || item.title || '';
    goodsImage = item.image || item.media || '';
  }

  // 跳转到聊天页面，传递商品信息
  wx.navigateTo({
    url: `/servicePackage/pages/messages/chat?targetId=${sellerId}&targetName=${sellerName}&goodsId=${goodsId}&goodsTitle=${goodsTitle}&goodsImage=${goodsImage}`
  });
}
```

## 数据来源说明

### 1. 我的订单列表
- **数据来源**：订单列表API + 交易快照（stuffSnapshot）
- **卖家ID获取**：
  1. 优先从 `item.stuffSnapshot.userId` 获取
  2. 兜底从 `item.sellerId` 或 `item.userId` 获取
- **商品信息**：从订单数据和交易快照中获取

### 2. 我的收藏列表
- **数据来源**：收藏列表API (`/users-api/v1/member/good-stuff/collect/page`)
- **卖家ID获取**：从 `item.userId` 等字段获取
- **商品信息**：从收藏数据中获取

### 3. 好物详情页
- **数据来源**：好物详情API
- **卖家ID获取**：从 `goods.userId` 获取
- **商品信息**：从好物详情数据中获取

## 聊天页面商品信息显示

当传递了商品相关参数时，聊天页面会在顶部显示商品信息卡片：

```javascript
// servicePackage/pages/messages/chat.js
if (goodsId) {
  // 如果传入了商品标题和图片，直接使用
  if (goodsTitle && goodsImage) {
    this.setData({
      goodsInfo: {
        id: goodsId,
        title: decodedGoodsTitle,
        price: price,
        image: decodedGoodsImage
      }
    });
  } else {
    // 否则加载商品信息
    this.loadGoodsInfo(goodsId);
  }
}
```

## 修改的文件

1. **`profilePackage/pages/goods/my/my.js`**
   - 修复收藏列表卖家信息获取
   - 优化contactSeller方法，支持订单和收藏两种数据结构
   - 确保跳转时传递商品信息参数

## 测试场景

### 1. 我的订单列表 → 聊天页面
- ✅ 显示卖家信息（通过接口获取最新头像和用户名）
- ✅ 显示商品信息卡片
- ✅ 正常进行聊天

### 2. 我的收藏列表 → 聊天页面
- ✅ 显示卖家信息（通过接口获取最新头像和用户名）
- ✅ 显示商品信息卡片
- ✅ 正常进行聊天

### 3. 好物详情页 → 聊天页面
- ✅ 显示卖家信息（通过接口获取最新头像和用户名）
- ✅ 显示商品信息卡片
- ✅ 正常进行聊天

### 4. 订单详情页 → 聊天页面
- ✅ 显示卖家信息（通过接口获取最新头像和用户名）
- ✅ 显示商品信息卡片
- ✅ 正常进行聊天

## 调试信息

在收藏列表数据处理中添加了详细的调试信息：

```javascript
console.log('收藏商品数据处理:', {
  原始数据: item,
  处理后: favoriteItem,
  卖家信息: {
    sellerId: favoriteItem.sellerId,
    sellerName: favoriteItem.sellerName,
    可能的ID字段: {
      userId: item.userId,
      goodStuffMemberId: item.goodStuffMemberId,
      memberId: item.memberId,
      goodStuffUserId: item.goodStuffUserId,
      createBy: item.createBy
    }
  }
});
```

在联系卖家方法中也添加了详细的调试信息，方便排查问题。

## 预期效果

修复后，所有从我的好物页面（订单列表、收藏列表）跳转到聊天页面的场景都应该：

1. **正确显示卖家信息**：通过userId调用接口获取最新的用户名和头像
2. **显示商品信息卡片**：在聊天页面顶部显示商品的标题、图片等信息
3. **提供完整的聊天体验**：用户可以清楚地知道在和谁聊天，关于哪个商品

这样就实现了与好物详情页一致的用户体验。
