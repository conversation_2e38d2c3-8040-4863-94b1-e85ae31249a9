<!--points.wxml-->
<view class="container page-bottom-safe-area {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <view class="points-header">
    <view class="points-card">
      <view class="points-title">我的积分</view>
      <view class="points-value">{{isAuthenticated ? points : '---'}}</view>
      <view class="points-actions">
        <view class="points-action" bindtap="navigateWithAuth" data-url="/pages/points/earn/earn">
          <view class="action-icon">+</view>
          <view class="action-text">赚积分</view>
        </view>
        <view class="points-action" bindtap="navigateWithAuth" data-url="/pages/points/record/record">
          <view class="action-icon">
            <text>📋</text>
          </view>
          <view class="action-text">积分明细</view>
        </view>
        <view class="points-action" bindtap="navigateWithAuth" data-url="/pages/points/rules/rules">
          <view class="action-icon">
            <text>📜</text>
          </view>
          <view class="action-text">积分规则</view>
        </view>
        <view class="points-action" bindtap="navigateWithAuth" data-url="/pages/points/level/level">
          <view class="action-icon">VIP</view>
          <view class="action-text">会员等级</view>
        </view>
      </view>
    </view>
  </view>

  <view class="mall-section">
    <view class="section-header">
      <view class="section-title">
        积分商城
        <view class="section-subtitle">用积分兑换好礼</view>
      </view>
      <view class="section-more" bindtap="navigateToMall">
        <text>查看全部</text>
        <text>></text>
      </view>
    </view>

    <view class="category-tabs">
      <view class="category-tab {{currentCategory === 'all' ? 'active' : ''}}"
            bindtap="switchCategory" data-category="all">全部</view>
      <view class="category-tab {{currentCategory === 'daily' ? 'active' : ''}}"
            bindtap="switchCategory" data-category="daily">日用好物</view>
      <view class="category-tab {{currentCategory === 'service' ? 'active' : ''}}"
            bindtap="switchCategory" data-category="service">物业服务</view>
      <view class="category-tab {{currentCategory === 'digital' ? 'active' : ''}}"
            bindtap="switchCategory" data-category="digital">数码家电</view>
    </view>

    <view class="product-grid">
      <view class="product-item" wx:for="{{filteredProducts}}" wx:key="id" bindtap="showProductDetail">
        <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="product-info">
          <view class="product-name">{{item.name}}</view>
          <view class="product-points">{{item.points}}积分</view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 商品详情弹窗 - 暂时注释掉 -->
<view class="product-detail-modal {{showProductModal ? 'show' : ''}} {{darkMode ? 'darkMode' : ''}}" bindtap="closeProductModal" data-darkmode="{{darkMode}}">
  <view class="product-detail-content {{darkMode ? 'darkMode' : ''}}" catchtap="stopPropagation" data-darkmode="{{darkMode}}">
    <view class="product-detail-close" bindtap="closeProductModal">×</view>
    <image class="product-detail-image" src="{{currentProduct.image}}" mode="aspectFill"></image>
    <view class="product-detail-info">
      <view class="product-detail-name">{{currentProduct.name}}</view>
      <view class="product-detail-points">{{currentProduct.points}}积分</view>
      <view class="product-detail-desc">{{currentProduct.description}}</view>
    </view>
    <view class="product-detail-footer">
      <button class="product-detail-btn {{isAuthenticated && points >= currentProduct.points ? '' : 'disabled'}}"
              bindtap="exchangeProduct"
              disabled="{{!isAuthenticated || points < currentProduct.points}}">
        {{isAuthenticated ? (points >= currentProduct.points ? '立即兑换' : '积分不足') : '请先认证'}}
      </button>
    </view>
  </view>
</view>

