# 微信小程序URL参数编码问题修复

## 问题描述

在微信小程序中，页面跳转时传递中文参数出现了双重编码问题，导致接收到的参数是 `%E6%88%91%E6%9F%A5%E6%88%91%E8%87%AA%E5%B7%B1` 这样的编码格式，而不是正常的中文文本。

## 问题根源

**微信小程序的wx.navigateTo会自动对URL参数进行编码**，如果我们在代码中手动使用 `encodeURIComponent` 进行编码，就会造成双重编码：

1. **第一次编码**：我们手动调用 `encodeURIComponent('用户名')`
2. **第二次编码**：微信小程序框架自动对URL参数进行编码

结果：`用户名` → `%E7%94%A8%E6%88%B7%E5%90%8D` → `%25E7%2594%25A8%25E6%2588%25B7%25E5%2590%258D`

## 解决方案

**不要手动编码URL参数，让微信小程序框架自动处理。**

### 修复前的错误做法

```javascript
// ❌ 错误：手动编码导致双重编码
const jumpUrl = `/servicePackage/pages/messages/chat?targetId=${userId}&targetName=${encodeURIComponent(userName)}&targetAvatar=${encodeURIComponent(avatarUrl)}`;

wx.navigateTo({
  url: jumpUrl
});
```

### 修复后的正确做法

```javascript
// ✅ 正确：不手动编码，让框架自动处理
const jumpUrl = `/servicePackage/pages/messages/chat?targetId=${userId}&targetName=${userName}&targetAvatar=${avatarUrl}`;

wx.navigateTo({
  url: jumpUrl
});
```

## 修复的文件

### 1. pages/messages/messages.js - 站内私信跳转

```javascript
// 修复前
const targetName = encodeURIComponent(userName);
const targetAvatar = encodeURIComponent(avatarUrl);
const jumpUrl = `/servicePackage/pages/messages/chat?targetId=${userId}&targetName=${targetName}&targetAvatar=${targetAvatar}`;

// 修复后
const jumpUrl = `/servicePackage/pages/messages/chat?targetId=${userId}&targetName=${userName}&targetAvatar=${avatarUrl}`;
```

### 2. profilePackage/pages/goods/detail/detail.js - 好物详情页联系卖家

```javascript
// 修复前
wx.navigateTo({
  url: `/servicePackage/pages/messages/chat?targetId=${sellerId}&targetName=${encodeURIComponent(sellerName)}&goodsTitle=${encodeURIComponent(goodsTitle)}&targetAvatar=${encodeURIComponent(sellerAvatar)}`
});

// 修复后
wx.navigateTo({
  url: `/servicePackage/pages/messages/chat?targetId=${sellerId}&targetName=${sellerName}&goodsTitle=${goodsTitle}&targetAvatar=${sellerAvatar}`
});
```

### 3. servicePackage/pages/messages/chat.js - 聊天页面参数接收

```javascript
// 修复前：复杂的多重解码逻辑
const safeDecodeURIComponent = (str) => {
  // 复杂的多次解码逻辑...
};

// 修复后：标准的单次解码
const safeDecodeURIComponent = (str) => {
  if (!str) return '';
  
  try {
    return decodeURIComponent(str);
  } catch (error) {
    console.error('解码失败:', str, error);
    return str;
  }
};
```

## 微信小程序URL参数处理机制

### 1. 发送端（wx.navigateTo）
- 微信小程序会自动对URL中的参数进行编码
- 特殊字符、中文等会被转换为URL安全的格式
- **不需要手动调用encodeURIComponent**

### 2. 接收端（onLoad的options）
- 微信小程序会自动对参数进行一次解码
- 如果原始参数没有被手动编码，接收到的就是正常文本
- 如果原始参数被手动编码了，接收到的就是编码格式，需要再次解码

### 3. 正确的处理流程

```
发送端: 用户名 → wx.navigateTo (自动编码) → URL中的%E7%94%A8%E6%88%B7%E5%90%8D
接收端: URL中的%E7%94%A8%E6%88%B7%E5%90%8D → onLoad (自动解码) → 用户名
```

## 最佳实践

### 1. URL参数传递
```javascript
// ✅ 推荐：直接传递原始值
wx.navigateTo({
  url: `/pages/target?name=${name}&avatar=${avatar}`
});

// ❌ 不推荐：手动编码
wx.navigateTo({
  url: `/pages/target?name=${encodeURIComponent(name)}&avatar=${encodeURIComponent(avatar)}`
});
```

### 2. 参数接收
```javascript
onLoad: function(options) {
  const { name, avatar } = options;
  
  // ✅ 推荐：如果发送端没有手动编码，直接使用
  this.setData({
    userName: name,
    userAvatar: avatar
  });
  
  // ✅ 如果不确定是否被编码，可以安全解码
  const safeName = this.safeDecodeURIComponent(name);
  const safeAvatar = this.safeDecodeURIComponent(avatar);
}

safeDecodeURIComponent: function(str) {
  if (!str) return '';
  
  try {
    return decodeURIComponent(str);
  } catch (error) {
    return str; // 解码失败返回原值
  }
}
```

### 3. 特殊字符处理
对于包含特殊字符的参数，微信小程序会自动处理：

```javascript
// 这些都会被正确处理
const params = {
  name: '张三',
  message: 'Hello World!',
  url: 'https://example.com/path?param=value',
  special: '特殊字符 & 符号 = 测试'
};

// 直接拼接到URL中即可
wx.navigateTo({
  url: `/pages/target?name=${params.name}&message=${params.message}`
});
```

## 调试技巧

### 1. 发送端调试
```javascript
const jumpUrl = `/pages/target?name=${name}`;
console.log('跳转URL:', jumpUrl);
console.log('原始参数:', { name });

wx.navigateTo({ url: jumpUrl });
```

### 2. 接收端调试
```javascript
onLoad: function(options) {
  console.log('接收到的原始options:', options);
  console.log('name参数:', options.name);
  console.log('name类型:', typeof options.name);
  
  // 尝试解码
  try {
    const decoded = decodeURIComponent(options.name);
    console.log('解码后:', decoded);
  } catch (error) {
    console.log('解码失败，使用原值');
  }
}
```

## 总结

1. **不要手动编码URL参数** - 微信小程序会自动处理
2. **接收端使用标准解码** - 一次 `decodeURIComponent` 即可
3. **添加容错处理** - 解码失败时使用原值
4. **充分测试** - 确保中文、特殊字符都能正确传递

通过这些修复，现在所有页面跳转的参数传递都能正确处理中文和特殊字符了。
