# 多个问题修复总结

## 1. 创建工单页面添加紧急程度字段

### 问题描述
创建工单页面缺少紧急程度level字段，需要查询字典值work_order_level，下拉选择，传nameEn。

### 修复内容

#### 1.1 WXML修改 (`servicePackage/pages/repair/repair.wxml`)
在工单类型后添加紧急程度选择：
```xml
<!-- 紧急程度 -->
<view class="form-card">
  <view class="form-card-title">
    <view class="title-icon-wrapper">
      <view class="icon-level"></view>
    </view>
    <view class="title-text">紧急程度</view>
    <text class="required">*</text>
  </view>

  <picker bindchange="selectLevel" value="{{selectedLevelIndex}}" range="{{workOrderLevels}}" range-key="nameCn">
    <view class="picker-field">
      <text class="{{selectedLevel ? '' : 'placeholder'}}">{{selectedLevel ? selectedLevel.nameCn : '请选择紧急程度'}}</text>
      <view class="icon-arrow-down"></view>
    </view>
  </picker>
</view>
```

#### 1.2 JavaScript修改 (`servicePackage/pages/repair/repair.js`)

**添加数据字段：**
```javascript
data: {
  // 紧急程度
  workOrderLevels: [],
  selectedLevel: null,
  selectedLevelIndex: 0,
}
```

**添加字典获取方法：**
```javascript
// 获取紧急程度字典
getWorkOrderLevelDict: function () {
  try {
    const workOrderLevels = util.getDictByNameEn('work_order_level')[0].children;
    this.setData({
      workOrderLevels: workOrderLevels
    });
  } catch (error) {
    console.error('获取紧急程度字典失败:', error);
    // 设置默认值
    this.setData({
      workOrderLevels: [
        { nameEn: 'low', nameCn: '低' },
        { nameEn: 'medium', nameCn: '中' },
        { nameEn: 'high', nameCn: '高' },
        { nameEn: 'urgent', nameCn: '紧急' }
      ]
    });
  }
}
```

**添加选择方法：**
```javascript
// 选择紧急程度
selectLevel: function (e) {
  const index = e.detail.value;
  const selectedLevel = this.data.workOrderLevels[index];
  
  this.setData({
    selectedLevel: selectedLevel,
    selectedLevelIndex: index
  });
  
  this.checkCanSubmit();
}
```

**修改表单验证：**
```javascript
checkCanSubmit: function () {
  // 如果是物业人员，只需要检查问题描述和紧急程度
  if (this.data.isPropertyStaff) {
    const canSubmit = !!this.data.description && !!this.data.selectedLevel;
    this.setData({ canSubmit });
    return;
  }

  // 普通用户需要检查更多字段
  let canSubmit = true;
  
  // ... 其他验证逻辑
  
  // 检查紧急程度
  if (!this.data.selectedLevel) {
    canSubmit = false;
  }
  
  this.setData({ canSubmit });
}
```

**修改提交数据：**
```javascript
// 准备工单数据
const workOrderData = {
  type: this.data.selectedWorkOrderType,
  userDescribe: this.data.description,
  media: this.data.images.join(','),
  regionType: this.getRegionTypeValue(),
  region: this.getRegionValue(),
  residentName: this.getResidentNameValue(),
  phone: this.getPhoneValue(),
  communityId: wx.getStorageSync('selectedCommunity').id,
  level: this.data.selectedLevel ? this.data.selectedLevel.nameEn : '', // 紧急程度
}
```

## 2. 修复游客模式访问限制

### 问题描述
用户注销后处于游客状态，但仍能查询到住户信息和进入报事报修页面。

### 修复内容

#### 2.1 修改用户认证检查 (`utils/util.js`)
```javascript
// 检查用户角色和认证状态
// 游客模式不能使用需要认证的功能
if (userInfo.role === 'tourist') {
  return {
    isAuthenticated: false,
    userInfo: userInfo,
    message: '游客模式无法使用此功能，请先实名认证'
  };
}

const isAuthenticated = userInfo.role === 'user';

return {
  isAuthenticated: isAuthenticated,
  userInfo: userInfo,
  message: isAuthenticated ? '已实名认证' : '请完成实名认证'
};
```

### 预期效果
- 游客模式用户无法进入报事报修页面
- 游客模式用户无法查询住户信息
- 提示用户需要先实名认证

## 3. 修复工单详情页挂起状态按钮

### 问题描述
工单挂起状态时，应该显示"取消工单"和"继续处理"按钮，但显示的是"取消工单"和"完成工单"按钮。

### 修复内容

#### 3.1 修改按钮显示逻辑 (`propertyPackage/pages/property/workorder/detail/index.js`)
```javascript
case 'pending': // 挂起状态
  buttonState.showProcessBtn = true; // 显示继续处理按钮
  break;
```

### 预期效果
- 挂起状态显示"取消工单"和"继续处理"按钮
- 点击"继续处理"按钮可以恢复工单处理

## 4. 修复访客时间检查资源浪费

### 问题描述
访客凭证页面的时间检查定时器在其他页面也在继续运行，浪费资源。

### 修复内容

#### 4.1 优化定时器管理 (`servicePackage/pages/visitor/credential/index.js`)

**添加生命周期方法：**
```javascript
onShow: function () {
  console.log('访客凭证页面显示，启动定时器');
  // 页面显示时刷新数据
  this.getVisitorData();
  
  // 启动定时器
  this.startTimer();
},

onHide: function () {
  console.log('访客凭证页面隐藏，清除定时器');
  // 页面隐藏时清除定时器，避免在其他页面浪费资源
  this.clearTimer();
},

onUnload: function () {
  console.log('访客凭证页面卸载，清除定时器');
  // 页面卸载时清除定时器
  this.clearTimer();
}
```

**添加定时器管理方法：**
```javascript
// 启动定时器
startTimer: function () {
  // 先清除现有定时器
  this.clearTimer();
  
  // 更新当前时间
  this.updateCurrentTime();
  
  // 启动新的定时器
  this.timer = setInterval(() => {
    this.updateCurrentTime();
  }, 1000);
  
  console.log('访客时间检查定时器已启动');
},

// 清除定时器
clearTimer: function () {
  if (this.timer) {
    clearInterval(this.timer);
    this.timer = null;
    console.log('访客时间检查定时器已清除');
  }
}
```

**修改onLoad方法：**
```javascript
// 获取访客数据
this.getVisitorData();

// 启动定时器
this.startTimer();
```

### 预期效果
- 只有在访客凭证页面显示时才运行时间检查
- 切换到其他页面时自动停止定时器
- 返回访客凭证页面时自动重启定时器
- 减少不必要的资源消耗

## 修改的文件总结

1. **`servicePackage/pages/repair/repair.wxml`** - 添加紧急程度选择器
2. **`servicePackage/pages/repair/repair.js`** - 添加紧急程度相关逻辑
3. **`utils/util.js`** - 修复游客模式认证检查
4. **`propertyPackage/pages/property/workorder/detail/index.js`** - 修复挂起状态按钮
5. **`servicePackage/pages/visitor/credential/index.js`** - 优化定时器管理

## 测试验证

### 1. 紧急程度字段测试
- 创建工单时能选择紧急程度
- 必须选择紧急程度才能提交
- 提交的数据包含level字段

### 2. 游客模式测试
- 游客模式无法进入报事报修
- 显示相应的提示信息

### 3. 工单挂起状态测试
- 挂起状态显示正确的按钮
- 继续处理功能正常

### 4. 访客时间检查测试
- 只在访客凭证页面运行定时器
- 切换页面时定时器停止
- 控制台日志显示定时器状态
