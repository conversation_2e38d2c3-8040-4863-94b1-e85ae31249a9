# 消息中心弹窗优化

## 修改概述

对消息中心列表页的消息详情弹窗进行了优化，主要包括：

1. 将图片显示位置调整到内容上方
2. 移除"相关图片"标题文字
3. 优化富文本内容显示，保留HTML样式
4. 为站内私信传入用户头像和用户名参数

## 详细修改内容

### 1. WXML 结构调整

#### 1.1 弹窗内容布局优化
```xml
<!-- 修改前 -->
<view class="modal-body">
  <view class="modal-time">{{currentMessage.time}}</view>
  <rich-text class="modal-content">{{currentMessage.content || '暂无内容'}}</rich-text>
  
  <view class="modal-images" wx:if="{{currentMessage.imageList && currentMessage.imageList.length > 0}}">
    <view class="images-title">相关图片</view>
    <view class="images-grid">
      <view class="image-item" wx:for="{{currentMessage.imageList}}" wx:key="index" bindtap="previewImage" data-src="{{item}}">
        <image src="{{item}}" mode="aspectFill" class="message-image"></image>
      </view>
    </view>
  </view>
</view>

<!-- 修改后 -->
<view class="modal-body">
  <view class="modal-time">{{currentMessage.time}}</view>
  
  <!-- 图片展示（放在内容上面，不显示标题） -->
  <view class="modal-images" wx:if="{{currentMessage.imageList && currentMessage.imageList.length > 0}}">
    <view class="images-grid">
      <view class="image-item" wx:for="{{currentMessage.imageList}}" wx:key="index" bindtap="previewImage" data-src="{{item}}">
        <image src="{{item}}" mode="aspectFill" class="message-image"></image>
      </view>
    </view>
  </view>

  <!-- 富文本内容 -->
  <view class="modal-content-wrapper">
    <rich-text class="modal-content rich-text-content" nodes="{{currentMessage.richTextNodes}}"></rich-text>
  </view>
</view>
```

### 2. JavaScript 功能增强

#### 2.1 富文本解析方法
```javascript
// 解析富文本内容
parseRichText: function(htmlContent) {
  if (!htmlContent) {
    return [];
  }

  try {
    // 创建富文本节点数组
    const nodes = [];
    
    // 简单的HTML解析，转换为rich-text支持的节点格式
    let processedContent = htmlContent
      // 处理HTML实体
      .replace(/&nbsp;/g, ' ')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"');

    // 处理段落
    processedContent = processedContent.replace(/<p[^>]*>(.*?)<\/p>/gi, (match, content) => {
      return content + '\n\n';
    });

    // 处理换行
    processedContent = processedContent.replace(/<br[^>]*>/gi, '\n');

    // 处理加粗文本
    processedContent = processedContent.replace(/<strong[^>]*>(.*?)<\/strong>/gi, (match, content) => {
      nodes.push({
        name: 'span',
        attrs: {
          style: 'font-weight: bold; color: #222;'
        },
        children: [{
          type: 'text',
          text: content
        }]
      });
      return `__BOLD_${nodes.length - 1}__`;
    });

    // 处理斜体文本
    processedContent = processedContent.replace(/<em[^>]*>(.*?)<\/em>/gi, (match, content) => {
      nodes.push({
        name: 'span',
        attrs: {
          style: 'font-style: italic; color: #555;'
        },
        children: [{
          type: 'text',
          text: content
        }]
      });
      return `__ITALIC_${nodes.length - 1}__`;
    });

    // 移除其他HTML标签
    processedContent = processedContent.replace(/<[^>]+>/g, '');
    
    // 清理多余的换行
    processedContent = processedContent.replace(/\n{3,}/g, '\n\n').trim();

    // 构建最终的节点数组
    const finalNodes = [];
    let currentText = processedContent;
    
    // 替换占位符为实际节点
    nodes.forEach((node, index) => {
      const placeholder = `__BOLD_${index}__` || `__ITALIC_${index}__`;
      if (currentText.includes(placeholder)) {
        const parts = currentText.split(placeholder);
        if (parts[0]) {
          finalNodes.push({
            type: 'text',
            text: parts[0]
          });
        }
        finalNodes.push(node);
        currentText = parts[1] || '';
      }
    });

    // 添加剩余文本
    if (currentText) {
      finalNodes.push({
        type: 'text',
        text: currentText
      });
    }

    return finalNodes.length > 0 ? finalNodes : processedContent;
  } catch (error) {
    console.error('解析富文本失败:', error);
    return htmlContent;
  }
}
```

#### 2.2 消息详情显示优化
```javascript
// 在showMessageDetail方法中添加富文本处理
let content = this.generateMessageContent(message);
let richTextNodes = this.parseRichText(content);

this.setData({
  showMessageModal: true,
  currentMessage: {
    ...message,
    content,
    richTextNodes, // 添加处理后的富文本节点
    imageList
  },
  currentMessageIcon: icon
});
```

#### 2.3 站内私信跳转优化
```javascript
handlePrivateMessageTap: function (e) {
  const message = e.currentTarget.dataset.message;
  
  // 处理头像URL，确保是完整的URL
  let avatarUrl = '';
  if (message.avatarUrl) {
    if (!message.avatarUrl.startsWith('http')) {
      avatarUrl = this.data.apiUrl + message.avatarUrl;
    } else {
      avatarUrl = message.avatarUrl;
    }
  }
  
  console.log('跳转到聊天页面，参数:', {
    targetId: message.userId,
    targetName: message.userName,
    targetAvatar: avatarUrl
  });

  // 跳转到聊天页面，传入完整的头像URL和用户名作为标题
  wx.navigateTo({
    url: `/servicePackage/pages/messages/chat?targetId=${message.userId}&targetName=${encodeURIComponent(message.userName)}&targetAvatar=${encodeURIComponent(avatarUrl)}&title=${encodeURIComponent(message.userName)}`
  });
}
```

### 3. CSS 样式优化

#### 3.1 图片布局调整
```css
/* 图片展示区域（放在内容上面） */
.modal-images {
  margin-bottom: 24rpx;
}

.images-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
```

#### 3.2 富文本容器样式
```css
/* 富文本内容容器 */
.modal-content-wrapper {
  margin-top: 16rpx;
}

.modal-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  word-break: break-all;
  white-space: pre-wrap;
  min-height: 80rpx;
}

/* 富文本样式增强 */
.rich-text-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
  word-break: break-word;
  white-space: pre-wrap;
}
```

## 功能特点

### 1. 图片显示优化
- **位置调整**：图片显示在内容上方，更符合阅读习惯
- **标题移除**：去掉"相关图片"标题，界面更简洁
- **布局优化**：图片网格布局，支持多图展示

### 2. 富文本样式保留
- **HTML解析**：将HTML标签转换为rich-text支持的节点格式
- **样式保留**：保留加粗、斜体等基本样式
- **实体转换**：正确处理HTML实体字符

### 3. 站内私信增强
- **头像传递**：确保头像URL完整性，支持相对路径转换
- **标题设置**：传入用户名作为聊天页面标题
- **参数编码**：使用encodeURIComponent确保特殊字符正确传递

### 4. 用户体验提升
- **视觉层次**：图片在上，内容在下，层次清晰
- **样式丰富**：富文本样式得到保留，阅读体验更好
- **信息完整**：聊天页面获得完整的用户信息

## 技术要点

### 1. Rich-text组件使用
- 使用`nodes`属性而非直接传入HTML字符串
- 构建符合微信小程序规范的节点数组
- 处理样式内联到节点属性中

### 2. URL处理
- 检查头像URL是否为完整路径
- 自动添加API前缀处理相对路径
- 使用encodeURIComponent编码URL参数

### 3. 样式兼容
- 移除不支持的CSS深度选择器
- 使用标准CSS选择器
- 通过节点属性传递样式

## 修改文件

1. **`pages/messages/messages.wxml`** - 调整弹窗布局结构
2. **`pages/messages/messages.js`** - 添加富文本解析和参数传递
3. **`pages/messages/messages.wxss`** - 优化样式布局

## 注意事项

1. **富文本限制**：微信小程序rich-text组件对HTML支持有限，复杂样式可能无法完全保留
2. **图片加载**：确保图片URL的正确性，支持相对路径和绝对路径
3. **参数传递**：使用URL编码确保特殊字符正确传递
4. **样式兼容**：避免使用微信小程序不支持的CSS特性
