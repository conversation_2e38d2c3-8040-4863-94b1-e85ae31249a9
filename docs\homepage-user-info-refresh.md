# 首页用户信息自动刷新功能

## 问题描述

首页只在初始化时获取用户信息和员工认证信息，在其他页面返回时没有刷新这些数据，导致无法及时获取后台更新的信息（如审核状态变更）。

## 解决方案

在首页的onShow方法中添加用户信息刷新逻辑，但要避免与onLoad方法产生重复请求。

## 实现机制

### 1. 页面状态管理

添加页面状态标志来区分首次加载和后续显示：

```javascript
data: {
  // 页面状态标志
  isFirstLoad: true, // 是否是首次加载
  lastRefreshTime: 0, // 上次刷新时间
  // ... 其他数据
}
```

### 2. onLoad方法

```javascript
onLoad: function () {
  console.log('=== 首页onLoad开始 ===');
  
  // 标记为首次加载
  this.setData({
    isFirstLoad: true,
    lastRefreshTime: Date.now()
  });
  
  this.initPage()
  
  // 开始完整的初始化流程
  this.startInitializationFlow()
  
  // 初始化积分服务
  PointsService.init()
}
```

### 3. onShow方法

```javascript
onShow: function () {
  console.log('📱 === 首页onShow开始 ===');
  console.log('📱 当前isFirstLoad状态:', this.data.isFirstLoad);
  
  // 更新底部tabbar选中状态
  TabbarManager.setTabbarSelected(this, 0)

  // 检查是否需要重新初始化（比如从小区选择页面返回）
  this.checkAndReinitialize()
  
  // 如果不是首次加载，刷新用户相关信息
  if (!this.data.isFirstLoad) {
    console.log('📱 非首次加载，开始刷新用户相关信息');
    this.refreshUserRelatedInfo();
  } else {
    console.log('📱 首次加载，标记为非首次加载');
    // 首次加载完成后，标记为非首次加载
    this.setData({
      isFirstLoad: false
    });
  }
}
```

### 4. 用户信息刷新方法

```javascript
// 刷新用户相关信息（避免与onLoad重复请求）
refreshUserRelatedInfo: function() {
  console.log('🔄 === 刷新用户相关信息开始 ===');
  
  // 检查是否需要刷新（避免频繁刷新）
  const now = Date.now();
  const timeSinceLastRefresh = now - this.data.lastRefreshTime;
  const minRefreshInterval = 5 * 1000; // 最小刷新间隔5秒
  
  if (timeSinceLastRefresh < minRefreshInterval) {
    console.log('⏰ 距离上次刷新时间过短，跳过刷新');
    return;
  }
  
  // 更新刷新时间
  this.setData({
    lastRefreshTime: now
  });
  
  // 检查登录状态
  const accessToken = wx.getStorageSync('access_token');
  if (!accessToken) {
    console.log('❌ 未登录，跳过用户信息刷新');
    return;
  }
  
  // 检查小区选择状态
  const selectedCommunity = wx.getStorageSync('selectedCommunity');
  if (!selectedCommunity || !selectedCommunity.id) {
    console.log('❌ 未选择小区，跳过用户信息刷新');
    return;
  }
  
  console.log('✅ 开始刷新用户信息和员工认证信息...');
  
  // 刷新用户信息
  this.refreshUserInfo()
    .then(() => {
      console.log('✅ 用户信息刷新完成');
      // 刷新员工认证信息
      return this.refreshPropertyAuthInfo();
    })
    .then(() => {
      console.log('✅ 员工认证信息刷新完成');
    })
    .catch(error => {
      console.error('❌ 刷新用户相关信息失败:', error);
    });
}
```

### 5. 员工认证信息刷新方法

```javascript
// 刷新员工认证信息
refreshPropertyAuthInfo: function() {
  return new Promise((resolve, reject) => {
    console.log('刷新员工认证信息...');
    
    // 检查是否为游客模式
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || userInfo.role === 'guest') {
      console.log('游客模式，跳过员工认证检查');
      resolve();
      return;
    }
    
    // 获取员工认证信息
    userApi.getPropertyAuth()
      .then(res => {
        console.log('员工认证信息获取成功:', res);
        
        if (res) {
          // 保存员工认证信息
          wx.setStorageSync('propertyAuth', res);
          
          // 更新页面状态
          const isPropertyStaff = res.status === 'active';
          this.setData({
            isPropertyStaff: isPropertyStaff
          });
          
          console.log('✅ 员工认证信息更新完成, isPropertyStaff:', isPropertyStaff);
        }
        
        resolve(res);
      })
      .catch(error => {
        console.error('获取员工认证信息失败:', error);
        // 不阻断流程，继续执行
        resolve();
      });
  });
}
```

## 刷新触发条件

### 1. 满足刷新的条件

- ✅ 不是首次加载（避免与onLoad重复）
- ✅ 距离上次刷新超过最小间隔（避免频繁刷新）
- ✅ 用户已登录（有access_token）
- ✅ 已选择小区（有selectedCommunity）

### 2. 跳过刷新的情况

- ❌ 首次加载（onLoad已处理）
- ❌ 刷新间隔过短（防止频繁请求）
- ❌ 未登录状态
- ❌ 未选择小区

## 刷新的数据内容

### 1. 用户基本信息

- 用户名、头像等基本信息
- 实名认证状态
- 积分信息
- 其他用户相关数据

### 2. 员工认证信息

- 员工认证状态（pending/active/rejected）
- 员工权限信息
- 物业工作台访问权限

## 使用场景

### 1. 从认证页面返回

用户在实名认证或员工认证页面提交信息后，返回首页时能及时看到最新的认证状态。

### 2. 从审核相关页面返回

管理员在后台审核用户信息后，用户返回首页时能看到审核结果。

### 3. 从其他功能页面返回

用户在使用其他功能时，如果后台有信息更新，返回首页时能获取最新状态。

## 性能优化

### 1. 防止频繁刷新

- 设置最小刷新间隔（当前为5秒）
- 记录上次刷新时间
- 避免短时间内重复请求

### 2. 条件检查

- 检查登录状态，未登录时跳过
- 检查小区选择状态，未选择时跳过
- 检查游客模式，游客跳过员工认证

### 3. 错误处理

- 请求失败时不阻断其他流程
- 提供详细的日志信息便于调试
- 保持页面功能的正常运行

## 调试信息

添加了详细的调试日志，便于开发和测试：

```javascript
console.log('📱 === 首页onShow开始 ===');
console.log('🔄 === 刷新用户相关信息开始 ===');
console.log('✅ 开始刷新用户信息和员工认证信息...');
console.log('⏰ 距离上次刷新时间过短，跳过刷新');
console.log('❌ 未登录，跳过用户信息刷新');
```

## 测试验证

### 1. 首次加载测试

1. 清除小程序缓存
2. 重新打开小程序
3. 检查是否只有onLoad的初始化请求，没有重复请求

### 2. 返回刷新测试

1. 从首页跳转到其他页面
2. 返回首页
3. 检查是否触发了用户信息刷新请求

### 3. 频繁切换测试

1. 快速在首页和其他页面间切换
2. 检查是否有防频繁刷新机制生效

### 4. 状态更新测试

1. 在后台更改用户认证状态
2. 从其他页面返回首页
3. 检查页面是否显示最新状态

## 修改的文件

1. **`pages/index/index.js`**
   - 添加页面状态标志（isFirstLoad、lastRefreshTime）
   - 修改onLoad方法，设置首次加载标志
   - 修改onShow方法，添加刷新逻辑
   - 添加refreshUserRelatedInfo方法
   - 添加refreshPropertyAuthInfo方法
   - 增强调试日志

## 预期效果

修复后，首页将能够：

1. **及时获取最新数据** - 从其他页面返回时自动刷新用户信息
2. **避免重复请求** - 首次加载时不会产生重复的API调用
3. **防止频繁刷新** - 设置合理的刷新间隔，避免性能问题
4. **保持数据同步** - 确保页面显示的信息与后台数据一致

这样用户在进行认证、审核等操作后，返回首页就能立即看到最新的状态，大大提升了用户体验。
