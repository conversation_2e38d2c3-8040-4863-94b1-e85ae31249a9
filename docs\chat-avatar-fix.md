# 聊天页面头像显示问题修复

## 问题分析

在不同页面跳转到聊天页面时，头像没有正确显示的问题主要有以下几个原因：

1. **字段名不统一**：不同API返回的头像字段名不一致
2. **数据传递不完整**：某些页面没有传递头像数据
3. **URL编码问题**：参数被多次编码导致解析失败
4. **数据结构差异**：不同页面的数据结构不同

## 涉及的跳转场景

### 1. 商品详情页 → 聊天页面
- **文件**: `profilePackage/pages/goods/detail/detail.js`
- **方法**: `contactSeller`
- **数据来源**: 商品详情API
- **可能的头像字段**: `sellerAvatarUrl`, `userAvatarUrl`, `avatarUrl`, `userAvatar`

### 2. 订单详情页 → 聊天页面
- **文件**: `profilePackage/pages/goods/order/order.js`
- **方法**: `contactSeller`
- **数据来源**: 订单详情API + 商品快照
- **可能的头像字段**: `sellerAvatar`, `sellerAvatarUrl`, `userAvatar`, `avatarUrl`

### 3. 我的订单页面 → 聊天页面
- **文件**: `profilePackage/pages/goods/my/my.js`
- **方法**: `contactSeller`
- **数据来源**: 订单列表API
- **可能的头像字段**: `sellerAvatar`, `sellerAvatarUrl`, `userAvatar`, `avatarUrl`

### 4. 站内私信列表 → 聊天页面
- **文件**: `pages/messages/messages.js`
- **方法**: `handlePrivateMessageTap`
- **数据来源**: 私信列表API
- **可能的头像字段**: `avatarUrl`

## 修复方案

### 1. 统一头像字段处理

在每个跳转方法中，尝试多个可能的头像字段名：

```javascript
// 尝试多个可能的头像字段名
const sellerAvatar = data.sellerAvatarUrl || data.userAvatarUrl || data.avatarUrl || data.userAvatar || '';
```

### 2. 移除手动URL编码

不再手动使用 `encodeURIComponent`，让微信小程序自动处理：

```javascript
// ❌ 错误做法
wx.navigateTo({
  url: `/servicePackage/pages/messages/chat?targetName=${encodeURIComponent(name)}`
});

// ✅ 正确做法
wx.navigateTo({
  url: `/servicePackage/pages/messages/chat?targetName=${name}`
});
```

### 3. 增强调试信息

在每个跳转方法中添加详细的调试日志：

```javascript
console.log('完整数据:', data);
console.log('所有可能的头像字段:', {
  sellerAvatarUrl: data.sellerAvatarUrl,
  userAvatarUrl: data.userAvatarUrl,
  avatarUrl: data.avatarUrl,
  userAvatar: data.userAvatar
});
```

## 具体修复内容

### 1. 商品详情页修复

```javascript
// profilePackage/pages/goods/detail/detail.js
contactSeller: function() {
  const goods = this.data.goods;
  const sellerId = goods.userId || '';
  const sellerName = goods.userName || '卖家';
  // 尝试多个可能的头像字段名
  const sellerAvatar = goods.sellerAvatarUrl || goods.userAvatarUrl || goods.avatarUrl || goods.userAvatar || '';
  
  console.log('商品完整数据:', goods);
  console.log('联系卖家参数:', {
    sellerId, sellerName, sellerAvatar,
    '所有可能的头像字段': {
      sellerAvatarUrl: goods.sellerAvatarUrl,
      userAvatarUrl: goods.userAvatarUrl,
      avatarUrl: goods.avatarUrl,
      userAvatar: goods.userAvatar
    }
  });
  
  // 跳转（不手动编码）
  wx.navigateTo({
    url: `/servicePackage/pages/messages/chat?targetId=${sellerId}&targetName=${sellerName}&targetAvatar=${sellerAvatar}&goodsId=${goodsId}&goodsTitle=${goodsTitle}&goodsImage=${goodsImage}`
  });
}
```

### 2. 订单详情页修复

```javascript
// profilePackage/pages/goods/order/order.js
// 在订单数据处理中添加头像字段
const order = {
  // ... 其他字段
  sellerAvatar: orderData.sellerAvatar || orderData.sellerAvatarUrl || goodsInfo.userAvatar || goodsInfo.avatarUrl || goodsInfo.sellerAvatarUrl || '',
};

// 联系卖家方法
contactSeller: function() {
  const order = this.data.order;
  console.log('订单完整数据:', order);
  
  wx.navigateTo({
    url: `/servicePackage/pages/messages/chat?targetId=${order.sellerId}&targetName=${order.sellerName}&targetAvatar=${order.sellerAvatar || ''}&goodsId=${order.goodsId}&goodsTitle=${order.stuffDescribe}&goodsImage=${order.image}`
  });
}
```

### 3. 我的订单页面修复

```javascript
// profilePackage/pages/goods/my/my.js
contactSeller: function(e) {
  const order = e.currentTarget.dataset.order; // 从WXML传递完整订单数据
  
  const sellerId = order.sellerId || order.userId || '';
  const sellerName = order.sellerName || order.userName || '卖家';
  const sellerAvatar = order.sellerAvatar || order.sellerAvatarUrl || order.userAvatar || order.avatarUrl || '';
  
  console.log('我的订单联系卖家参数:', {
    sellerId, sellerName, sellerAvatar,
    '完整订单数据': order
  });
  
  wx.navigateTo({
    url: `/servicePackage/pages/messages/chat?targetId=${sellerId}&targetName=${sellerName}&targetAvatar=${sellerAvatar}&goodsId=${goodsId}&goodsTitle=${goodsTitle}&goodsImage=${goodsImage}`
  });
}
```

```xml
<!-- profilePackage/pages/goods/my/my.wxml -->
<!-- 修改前 -->
<view class="action-btn contact" bindtap="contactSeller" data-id="{{item.sellerId}}" data-name="{{item.sellerName}}">联系卖家</view>

<!-- 修改后 -->
<view class="action-btn contact" bindtap="contactSeller" data-order="{{item}}">联系卖家</view>
```

### 4. 站内私信列表修复

```javascript
// pages/messages/messages.js
handlePrivateMessageTap: function(e) {
  const message = e.currentTarget.dataset.message;
  
  const userId = message.userId || message.id;
  const userName = message.userName || message.title || '未知用户';
  
  // 处理头像URL
  let avatarUrl = '';
  if (message.avatarUrl) {
    if (!message.avatarUrl.startsWith('http')) {
      avatarUrl = this.data.apiUrl + message.avatarUrl;
    } else {
      avatarUrl = message.avatarUrl;
    }
  }
  
  // 跳转（不手动编码）
  wx.navigateTo({
    url: `/servicePackage/pages/messages/chat?targetId=${userId}&targetName=${userName}&targetAvatar=${avatarUrl}`
  });
}
```

## 调试建议

### 1. 检查数据源
在每个跳转方法中，先打印完整的数据对象：
```javascript
console.log('完整数据对象:', data);
console.log('数据对象的所有键:', Object.keys(data));
```

### 2. 检查头像字段
打印所有可能的头像字段：
```javascript
console.log('所有可能的头像字段:', {
  sellerAvatarUrl: data.sellerAvatarUrl,
  userAvatarUrl: data.userAvatarUrl,
  avatarUrl: data.avatarUrl,
  userAvatar: data.userAvatar,
  sellerAvatar: data.sellerAvatar
});
```

### 3. 检查跳转参数
在跳转前打印最终的URL：
```javascript
const jumpUrl = `/servicePackage/pages/messages/chat?targetId=${sellerId}&targetName=${sellerName}&targetAvatar=${sellerAvatar}`;
console.log('跳转URL:', jumpUrl);
```

### 4. 检查接收参数
在聊天页面的onLoad中打印接收到的参数：
```javascript
onLoad: function(options) {
  console.log('聊天页面接收参数:', options);
  console.log('targetAvatar原始值:', options.targetAvatar);
}
```

## 修改的文件

1. **`profilePackage/pages/goods/detail/detail.js`** - 商品详情页联系卖家
2. **`profilePackage/pages/goods/order/order.js`** - 订单详情页联系卖家
3. **`profilePackage/pages/goods/my/my.js`** - 我的订单页面联系卖家
4. **`profilePackage/pages/goods/my/my.wxml`** - 我的订单页面模板
5. **`pages/messages/messages.js`** - 站内私信跳转
6. **`servicePackage/pages/messages/chat.js`** - 聊天页面参数接收

## 预期效果

修复后，所有页面跳转到聊天页面时都应该能正确显示对方的头像，如果某个数据源确实没有头像信息，会显示默认头像。
