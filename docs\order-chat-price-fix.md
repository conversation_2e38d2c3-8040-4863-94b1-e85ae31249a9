# 订单详情页联系卖家价格显示修复

## 问题描述

在订单详情页点击联系卖家时，跳转到聊天页面，但商品价格没有显示。需要从交易快照中获取价格信息并传递给聊天页面。

## 问题分析

### 1. 原始问题

**跳转参数缺失价格：**
```javascript
// 修复前：缺少价格参数
wx.navigateTo({
  url: `/servicePackage/pages/messages/chat?targetId=${order.sellerId}&targetName=${order.sellerName}&goodsId=${order.goodsId}&goodsTitle=${order.stuffDescribe}&goodsImage=${order.image}`
});
```

### 2. 数据源分析

订单数据中价格信息可能存在于多个字段：
- `order.stuffSnapshot.price` - 交易快照中的价格（推荐）
- `order.price` - 订单价格字段
- `order.totalAmount` - 订单总金额

### 3. 聊天页面接收参数

聊天页面期望接收的参数名为 `price`：
```javascript
const { targetId, targetName, goodsId, targetAvatar, goodsTitle, goodsImage, price, title } = options
```

## 修复方案

### 1. 修改订单详情页联系卖家方法

#### 文件：`profilePackage/pages/goods/order/order.js`

**完整的修复代码：**
```javascript
// 联系卖家
contactSeller: function () {
  const order = this.data.order;
  if (!order) {
    wx.showToast({
      title: '订单信息不存在',
      icon: 'none'
    });
    return;
  }

  console.log('订单完整数据:', order);
  
  // 从交易快照中获取价格信息
  let goodsPrice = '';
  if (order.stuffSnapshot && order.stuffSnapshot.price) {
    goodsPrice = order.stuffSnapshot.price;
  } else if (order.price) {
    goodsPrice = order.price;
  } else if (order.totalAmount) {
    goodsPrice = order.totalAmount;
  }

  console.log('订单联系卖家参数:', {
    sellerId: order.sellerId,
    sellerName: order.sellerName,
    sellerAvatar: order.sellerAvatar,
    goodsId: order.goodsId,
    goodsTitle: order.stuffDescribe,
    goodsImage: order.image,
    goodsPrice: goodsPrice,
    '交易快照数据': order.stuffSnapshot,
    '所有可能的价格字段': {
      'stuffSnapshot.price': order.stuffSnapshot?.price,
      'order.price': order.price,
      'order.totalAmount': order.totalAmount
    }
  });

  // 构建跳转URL，包含价格信息
  const chatUrl = `/servicePackage/pages/messages/chat?targetId=${order.sellerId}&targetName=${encodeURIComponent(order.sellerName)}&goodsId=${order.goodsId}&goodsTitle=${encodeURIComponent(order.stuffDescribe)}&goodsImage=${encodeURIComponent(order.image)}&price=${goodsPrice}`;
  
  console.log('跳转到聊天页面URL:', chatUrl);

  // 跳转到聊天页面
  wx.navigateTo({
    url: chatUrl
  });
}
```

### 2. 价格获取优先级

按照以下优先级获取价格信息：

1. **第一优先级**：`order.stuffSnapshot.price`
   - 来源：交易快照中的商品价格
   - 最准确，反映下单时的实际价格

2. **第二优先级**：`order.price`
   - 来源：订单直接价格字段
   - 备用方案

3. **第三优先级**：`order.totalAmount`
   - 来源：订单总金额
   - 最后备用方案

### 3. URL编码处理

为了确保特殊字符正确传递，对以下参数进行URL编码：
- `targetName` - 卖家姓名
- `goodsTitle` - 商品标题
- `goodsImage` - 商品图片URL

```javascript
const chatUrl = `/servicePackage/pages/messages/chat?targetId=${order.sellerId}&targetName=${encodeURIComponent(order.sellerName)}&goodsId=${order.goodsId}&goodsTitle=${encodeURIComponent(order.stuffDescribe)}&goodsImage=${encodeURIComponent(order.image)}&price=${goodsPrice}`;
```

## 聊天页面价格显示

### 1. 参数接收

聊天页面正确接收价格参数：
```javascript
const { targetId, targetName, goodsId, targetAvatar, goodsTitle, goodsImage, price, title } = options
```

### 2. 商品信息设置

将价格信息设置到商品信息对象中：
```javascript
if (goodsTitle && goodsImage) {
  this.setData({
    goodsInfo: {
      id: goodsId,
      title: decodedGoodsTitle,
      price: price, // 价格信息
      image: decodedGoodsImage
    }
  });
}
```

### 3. 页面显示

在WXML中显示价格：
```xml
<!-- 商品信息卡片 -->
<view class="goods-card" wx:if="{{goodsInfo}}" bindtap="handleGoodsTap">
  <image class="goods-image" src="{{goodsInfo.image}}" mode="aspectFill"></image>
  <view class="goods-info">
    <view class="goods-title">{{goodsInfo.title}}</view>
    <view class="goods-price">¥{{goodsInfo.price}}</view>
  </view>
  <view class="goods-arrow">
    <image class="arrow-icon" src="/images/icons/arrow-right.svg"></image>
  </view>
</view>
```

## 调试信息

### 1. 订单数据日志

添加了详细的调试日志来跟踪价格获取过程：
```javascript
console.log('订单联系卖家参数:', {
  sellerId: order.sellerId,
  sellerName: order.sellerName,
  sellerAvatar: order.sellerAvatar,
  goodsId: order.goodsId,
  goodsTitle: order.stuffDescribe,
  goodsImage: order.image,
  goodsPrice: goodsPrice,
  '交易快照数据': order.stuffSnapshot,
  '所有可能的价格字段': {
    'stuffSnapshot.price': order.stuffSnapshot?.price,
    'order.price': order.price,
    'order.totalAmount': order.totalAmount
  }
});
```

### 2. URL跳转日志

记录完整的跳转URL：
```javascript
console.log('跳转到聊天页面URL:', chatUrl);
```

## 测试验证

### 1. 价格显示测试

1. 进入订单详情页
2. 点击"联系卖家"按钮
3. 检查聊天页面是否显示商品价格
4. 验证价格是否与订单中的价格一致

### 2. 不同价格字段测试

测试不同的价格数据源：
- 有 `stuffSnapshot.price` 的订单
- 只有 `order.price` 的订单
- 只有 `order.totalAmount` 的订单

### 3. 特殊字符测试

测试包含特殊字符的商品标题和卖家姓名：
- 中文字符
- 空格
- 特殊符号

## 修改的文件

1. **`profilePackage/pages/goods/order/order.js`**
   - 修改 `contactSeller` 方法
   - 添加价格获取逻辑
   - 添加URL编码处理
   - 增强调试日志

## 预期效果

修复后，从订单详情页联系卖家跳转到聊天页面时：

1. **正确显示价格** - 商品卡片中显示正确的价格信息
2. **价格来源准确** - 优先使用交易快照中的价格
3. **容错处理** - 当主要价格字段不存在时，使用备用字段
4. **字符编码正确** - 特殊字符正确传递和显示
5. **调试信息完整** - 便于排查问题和验证数据

## 注意事项

### 1. 价格格式

确保价格格式一致，建议：
- 数字格式：`"6999.00"`
- 显示格式：`"¥6999.00"`

### 2. 空值处理

当所有价格字段都为空时，`goodsPrice` 为空字符串，聊天页面应该有相应的处理逻辑。

### 3. 数据类型

价格可能是字符串或数字类型，需要确保聊天页面能正确处理两种类型。

这个修复确保了订单详情页到聊天页面的价格信息正确传递和显示，提升了用户体验。
