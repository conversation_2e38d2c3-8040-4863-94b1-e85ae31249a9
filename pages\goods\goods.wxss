/* goods.wxss */
.container {
  padding: 30rpx;
  padding-bottom: 200rpx; /* 为底部TabBar留出空间 */
  position: relative;
}

.page-header {
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}



.page-title {
  font-size: 44rpx;
  font-weight: 600;
  color: #333;
}

/* 布局切换按钮 */
.layout-toggle-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
  transition: all 0.3s;
}

.layout-toggle-btn:active {
  background-color: #e0e0e0;
}

/* 布局图标 */
.layout-icon {
  width: 32rpx;
  height: 32rpx;
  position: relative;
}

.grid-icon {
  background-image: radial-gradient(circle, #666 3rpx, transparent 3rpx);
  background-size: 12rpx 12rpx;
  background-position: 0 0, 16rpx 16rpx;
}

.list-icon {
  position: relative;
}

.list-icon:before,
.list-icon:after {
  content: "";
  position: absolute;
  left: 0;
  width: 32rpx;
  height: 4rpx;
  background-color: #666;
  border-radius: 2rpx;
}

.list-icon:before {
  top: 8rpx;
}

.list-icon:after {
  bottom: 8rpx;
}

.list-icon:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 32rpx;
  height: 4rpx;
  background-color: #666;
  border-radius: 2rpx;
  transform: translateY(-50%);
}



/* 搜索栏样式 */
.search-bar {
  margin-bottom: 30rpx;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 20rpx;
  height: 80rpx;
  border: 1rpx solid rgb(223, 218, 218);
}

.search-icon {
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}

.clear-icon {
  margin-left: 10rpx;
}

/* 分类导航样式 */
.category-scroll {
  margin-bottom: 30rpx;
  white-space: nowrap;
}

.category-nav {
  display: flex;
  padding: 0 30rpx;
}

.category-item {
  flex-shrink: 0;
  padding: 16rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
  border-radius: 30rpx;
  background-color: #f5f5f5;
  white-space: nowrap;
  transition: all 0.3s;
}

.category-item.active {
  color: #ffffff;
  background-color: #ff8c00;
}

/* 筛选和排序栏样式 */
.filter-bar {
  display: flex;
  margin-bottom: 20rpx;
  background: #f8f8f8;
  border-radius: 40rpx;
  padding: 8rpx;
  overflow-x: auto;
  white-space: nowrap;
}

.filter-item {
  padding: 0 24rpx;
  height: 64rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #666;
  border-radius: 32rpx;
  margin-right: 8rpx;
}

.filter-item:last-child {
  margin-right: 0;
}

.filter-item.active {
  background: white;
  color: #ff8c00;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 商品列表样式 - 列表模式 */
.goods-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 40rpx;
  padding: 0 20rpx;
}

.goods-card {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: transform 0.2s;
  margin-bottom: 16rpx;
}

.goods-card:active {
  transform: scale(0.98);
}

.card-container {
  display: flex;
}

.goods-image {
  width: 200rpx;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 16rpx 0 0 16rpx;
}

.goods-info {
  flex: 1;
  padding: 20rpx;
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
}

.goods-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.goods-tag-row {
  display: flex;
  margin-bottom: 12rpx;
}

.goods-condition-tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  background-color: #f8f8f8;
  color: #666;
  border-radius: 8rpx;
  margin-right: 10rpx;
}

.goods-free-tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  background-color: #e8f5e9;
  color: #4caf50;
  border-radius: 8rpx;
}

.goods-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff8c00;
  margin-bottom: 12rpx;
}

.goods-free-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #4caf50;
  margin-bottom: 12rpx;
}

.goods-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  margin-top: auto;
}

.goods-location {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.goods-user-avatar {
  flex-shrink: 0;
  
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.user-avatar-text {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #ff9500;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

.user-avatar-default {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjOTk5Ii8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzIDcgMTUuMzMgNyAxOFYyMEgxN1YxOEMxNyAxNS4zMyAxNC42NyAxMyAxMiAxNFoiIGZpbGw9IiM5OTkiLz4KPC9zdmc+');
  background-size: 24rpx 24rpx;
  background-repeat: no-repeat;
  background-position: center;
}

/* 商品列表样式 - 网格模式 */
.goods-list.grid-style {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  padding: 0 20rpx;
}

.goods-card.grid-item {
  margin-bottom: 0;
}

.goods-card.grid-item .card-container {
  flex-direction: column;
  height: auto;
}

.goods-card.grid-item .goods-image {
  width: 100%;
  height: 340rpx;
  border-radius: 16rpx 16rpx 0 0;
}

.goods-card.grid-item .goods-info {
  padding: 16rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.goods-card.grid-item .goods-name {
  font-size: 28rpx;
  -webkit-line-clamp: 2;
  margin-top: 12rpx;
  height: 80rpx;
}

.goods-card.grid-item .goods-tag-row {
  margin-bottom: 8rpx;
}

.goods-card.grid-item .goods-price,
.goods-card.grid-item .goods-free-text {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.goods-card.grid-item .goods-meta {
  font-size: 22rpx;
}

/* 无结果提示样式 */
.no-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.no-result-icon {
  margin-bottom: 20rpx;
}

.no-result-text {
  font-size: 28rpx;
  color: #999;
}

/* 发布按钮样式 */
.publish-btn {
  position: fixed;
  right: 40rpx;
  bottom: 300rpx;
  width: 100rpx;
  height: 100rpx;
  background: #ff8c00;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 140, 0, 0.3);
  z-index: 1000;
  transition: all 0.3s;
}

.plus-icon {
  color: white;
  font-size: 60rpx;
  font-weight: 300;
  line-height: 90rpx;
  text-align: center;
}

.publish-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 140, 0, 0.3);
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1c1c1e;
}

.darkMode .page-title {
  color: #f5f5f7;
}

.darkMode .layout-toggle-btn {
  background-color: #2c2c2e;
}

.darkMode .list-icon:before,
.darkMode .list-icon:after {
  background-color: #8e8e93;
}

.darkMode .grid-icon {
  background-image: radial-gradient(circle, #8e8e93 3rpx, transparent 3rpx);
}

.darkMode .search-input-wrapper {
  background-color: #2c2c2e;
}

.darkMode .search-input {
  color: #f5f5f7;
}

.darkMode .category-icon {
  background-color: #2c2c2e;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.darkMode .category-name {
  color: #8e8e93;
}

.darkMode .filter-bar {
  background: #2c2c2e;
}

.darkMode .filter-item {
  color: #8e8e93;
}

.darkMode .filter-item.active {
  background: #3a3a3c;
  color: #ff9500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.darkMode .goods-card {
  background: #2c2c2e;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.darkMode .category-item {
  background-color: #2c2c2e;
  color: #8e8e93;
}

.darkMode .category-item.active {
  background-color: #ff8c00;
  color: #ffffff;
}

.darkMode .goods-image {
  background-color: #3a3a3c;
}

.darkMode .goods-name {
  color: #f5f5f7;
}

.darkMode .goods-condition-tag {
  background-color: #3a3a3c;
  color: #8e8e93;
}

.darkMode .goods-free-tag {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.darkMode .goods-price {
  color: #ff9500;
}

.darkMode .goods-meta {
  color: #8e8e93;
}

.darkMode .no-result-text {
  color: #8e8e93;
}

.darkMode .publish-btn {
  background: #ff8c00;
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 140, 0, 0.2);
}
