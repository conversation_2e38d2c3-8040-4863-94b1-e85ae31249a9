// pages/goods/my/my.js
const util = require('@/utils/util.js')
const dateUtil = require('@/utils/dateUtil.js')
const goodsApi = require('@/api/goods.js')
const commApi = require('@/api/commApi.js')

Page({
  data: {
    darkMode: false,
    activeTab: 'published', // published, orders, favorites
    publishedGoods: [],
    orders: [],
    favorites: [],
    userInfo: null,
    apiUrl: '', // 图片访问路径

    // 搜索和筛选相关
    searchKeyword: '',
    currentCategory: 'all',
    currentType: 'all',
    currentOrderStatus: 'all', // 订单状态筛选
    sortType: 'newest', // newest, amount-asc, amount-desc

    statusOptions: [], // 商品状态字典
    typeOptions: [], // 商品类型字典
    categoryOptions: [], // 商品分类字典
    orderStatusOptions: [], // 订单状态字典
    orderIdentityOptions: [], // 订单身份字典（我的购买/我卖出的）
    currentOrderIdentity: 'buy', // 当前选择的订单身份，默认我的购买
    showCancelModal: false, // 是否显示取消原因选择弹窗
    cancelOrderId:null,
    cancelReasons: [ // 取消原因选项（前三个是单选项）
      { value: 'wrong_order', text: '拍错了' },
      { value: 'no_need', text: '不想要了' },
      { value: 'price_issue', text: '价格问题' }
    ],
    selectedCancelReason: '',
    customCancelReason: '',

    // 原始数据存储（用于筛选）
    originalPublishedGoods: [],
    originalFavorites: [],
    originalOrders: []
  },

  onLoad: function (options) {
    // 初始化图片访问路径
    this.setData({
      apiUrl: wx.getStorageSync('apiUrl') + '/common-api/v1/file/'
    });

    // 如果有传入的tab参数，则切换到对应的tab
    if (options.tab && ['published', 'orders', 'favorites'].includes(options.tab)) {
      this.setData({
        activeTab: options.tab
      });

      // 如果是订单tab，确保默认选择第一个字典项
      if (options.tab === 'orders') {
        // 这里先设置默认值，字典加载完成后会在loadDictionaries中更新
        this.setData({
          currentOrderIdentity: 'buy'
        });
      }
    }

    // 加载字典数据
    this.loadDictionaries();

    // 获取用户信息
    this.getUserInfo()


    // 加载数据
    this.loadData()
  },

  // 加载字典数据
  loadDictionaries: function () {
    try {
      // 使用统一的字典获取方法
      const statusDict = util.getDictByNameEn('good_stuff_status');
      const typeDict = util.getDictByNameEn('good_stuff_type');
      const categoryDict = util.getDictByNameEn('good_stuff_category');

      const orderStatusDict = util.getDictByNameEn('good_stuff_order_status');

      const orderIdentityDict = util.getDictByNameEn('goods_order_identities');

      // 设置订单身份选项，优先使用字典数据
      let orderIdentityOptions = [];
      if (orderIdentityDict && orderIdentityDict.length > 0 && orderIdentityDict[0].children) {
        orderIdentityOptions = orderIdentityDict[0].children;
      } else {
        // 字典为空时使用默认值
        orderIdentityOptions = [
          { nameCn: '我的购买', nameEn: 'buy' },
          { nameCn: '我卖出的', nameEn: 'sell' }
        ];
      }

      // 设置默认选中第一个字典项
      let defaultOrderIdentity = 'buy';
      if (orderIdentityOptions.length > 0) {
        defaultOrderIdentity = orderIdentityOptions[0].nameEn;
      }

      this.setData({
        statusOptions: statusDict && statusDict.length > 0 && statusDict[0].children ? statusDict[0].children : [],
        typeOptions: typeDict && typeDict.length > 0 && typeDict[0].children ? typeDict[0].children : [],
        categoryOptions: categoryDict && categoryDict.length > 0 && categoryDict[0].children ? categoryDict[0].children : [],
        orderStatusOptions: orderStatusDict && orderStatusDict.length > 0 && orderStatusDict[0].children ? orderStatusDict[0].children : [],
        orderIdentityOptions: orderIdentityOptions,
        currentOrderIdentity: defaultOrderIdentity // 设置为第一个字典项
      });

      console.log('字典数据加载完成:', {
        statusOptions: this.data.statusOptions,
        typeOptions: this.data.typeOptions,
        categoryOptions: this.data.categoryOptions,
        orderStatusOptions: this.data.orderStatusOptions,
        orderIdentityOptions: this.data.orderIdentityOptions
      });
    } catch (error) {
      console.error('加载字典数据失败:', error);
      // 设置空数组作为默认值
      this.setData({
        statusOptions: [],
        typeOptions: [],
        categoryOptions: [],
        orderStatusOptions: [],
        orderIdentityOptions: []
      });
    }
  },

  onShow: function () {
    // 每次显示页面时刷新数据
    this.loadData()
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.loadData().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 切换标签页
  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab

    // 重置筛选条件
    this.setData({
      activeTab: tab,
      searchKeyword: '',
      currentCategory: 'all',
      currentType: 'all',
      currentOrderStatus: 'all',
      sortType: 'newest'
    })

    // 如果数据为空，则加载数据
    if (tab === 'published' && this.data.publishedGoods.length === 0) {
      this.loadPublishedGoods()
    } else if (tab === 'orders') {
      // 切换到我的订单时，确保选择第一个字典项，并重新加载数据
      let firstIdentity = 'buy'; // 默认值
      if (this.data.orderIdentityOptions.length > 0) {
        firstIdentity = this.data.orderIdentityOptions[0].nameEn;
      }
      this.setData({
        currentOrderIdentity: firstIdentity
      });
      this.loadOrders()
    } else if (tab === 'favorites' && this.data.favorites.length === 0) {
      this.loadFavorites()
    } else {
      // 如果已有数据，重新应用筛选
      this.applyFilters()
    }
  },

  // 加载所有数据
  loadData: function () {
    return Promise.all([
      this.loadPublishedGoods(),
      this.loadOrders(),
      this.loadFavorites()
    ])
  },

  // 获取用户信息
  getUserInfo: function () {
    // 从本地存储获取用户信息
    const userInfo = wx.getStorageSync('userInfo') || {};
    this.setData({
      userInfo: userInfo
    });
  },

  // 加载我发布的商品
  loadPublishedGoods: function () {

    var params={
      pageNum: 1,
      pageSize: 100
    }
    return goodsApi.getMyGoodsList(params).then(res => {
      console.log('获取我的发布商品:', res);

      if (res && res.list) {
        const publishedGoods = res.list.map(item => {
          const goods = {
            id: item.id,
            title: item.title || '',
            stuffDescribe: item.stuffDescribe || '',
            amount: item.amount || 0,
            media: item.media || '', // 保持原始media字段
            status: item.status || 'pending', // 保持原始状态值
            statusName: this.getGoodsStatus(item.status), // 状态显示名称
            statusClass: this.getGoodsStatusClass(item.status), // 状态样式类名
            examineNote: item.examineNote || '', // 审核原因
            stock: item.stock || 0,
            soldStock: item.soldStock || 0,
            views: item.views || 0,
            createTime: item.createTime ? dateUtil.formatTime(new Date(item.createTime)) : '',
            type: item.type || 'free',
            typeName: this.getGoodsTypeName(item.type), // 类型显示名称
            isFreeType: this.checkIsFreeType(item.type), // 动态判断是否免费类型
            categoryCode: item.categoryCode || '',
            categoryName: this.getGoodsCategoryName(item.categoryCode), // 分类显示名称
            address: item.address || '',
            pendingOrders: 0 // 待核销订单数，需要单独接口获取
          };

          // 处理图片URL
          if (goods.media) {
            goods.images = goods.media.split(',').map(image => {
              const trimmedImage = image.trim();
              if (trimmedImage && !trimmedImage.startsWith('http')) {
                return this.data.apiUrl + trimmedImage;
              }
              return trimmedImage;
            });
          } else {
            goods.images = [];
          }

          return goods;
        });





        this.setData({
          publishedGoods,
          originalPublishedGoods: publishedGoods // 保存原始数据用于筛选
        });
      } else {
        console.warn('获取我的发布商品失败:', res.message);
        this.setData({
          publishedGoods: []
        });
      }
    }).catch(err => {
      console.error('加载我发布的商品失败:', err);

    
      this.setData({
        publishedGoods: []
      });
    });
  },

  // 加载我的订单
  loadOrders: function () {
    return goodsApi.getMyGoodsOrderList({
      pageNum: 1,
      pageSize: 100,
      searchType: this.data.currentOrderIdentity // 使用当前选择的身份
    }).then(res => {
      console.log('获取我的订单:', res);
      if ( res && res.list) {
        const orders = res.list.map(order => {
          // 解析 stuffSnapshot 获取商品信息
          let goodsInfo = {};
          if (order.stuffSnapshot) {
            try {
              goodsInfo = JSON.parse(order.stuffSnapshot);
            } catch (e) {
              console.error('解析 stuffSnapshot 失败:', e);
              goodsInfo = {};
            }
          }

          const orderItem = {
            id: order.id,
            orderNo: order.orderNo || '',
            goodsId: goodsInfo.id || order.goodStuffId,
            title: goodsInfo.stuffDescribe || order.goodStuffDescribe || '', // 添加title字段用于搜索
            goodsTitle: goodsInfo.stuffDescribe || order.goodStuffDescribe || '', // 商品标题
            stuffDescribe: goodsInfo.stuffDescribe || order.goodStuffDescribe || '',
            amount: order.unitAmount || goodsInfo.amount || 0,
            totalAmount: order.totalAmount || 0,
            unitAmount: order.unitAmount || 0,
            image: goodsInfo.media ? goodsInfo.media.split(',')[0] : '',
            quantity: order.quantity || 1,
            status: order.status || 'pending',
            statusName: this.getOrderStatusName(order.status),
            createTime: order.createTime || '', // 保持原始时间格式用于排序
            updateTime: order.updateTime || '',
            createTimeFormatted: order.createTime ? dateUtil.formatTime(new Date(order.createTime)) : '', // 格式化时间用于显示
            updateTimeFormatted: order.updateTime ? dateUtil.formatTime(new Date(order.updateTime)) : '',
            sellerName: goodsInfo.userName || order.sellerName || '卖家',
            buyerName: order.buyerName || '买家', // 添加买家姓名用于搜索
            sellerId: order.sellerId || goodsInfo.userId,
            phone: order.phone || '',
            note: order.note || '',
            type: goodsInfo.type || order.goodStuffType || 'free',
            typeName: this.getGoodsTypeName(goodsInfo.type || order.goodStuffType),
            isFreeType: this.checkIsFreeType(goodsInfo.type || order.goodStuffType),
            categoryCode: goodsInfo.categoryCode || '', // 添加分类字段用于筛选
            address: goodsInfo.address || '',
            // 保存原始快照数据
            stuffSnapshot: goodsInfo
          };

          // 处理图片URL
          if (orderItem.image) {

            orderItem.medias = orderItem.image.split(',').map(image => {

              const trimmedImage = image.trim();
              if (trimmedImage && !trimmedImage.startsWith('http')) {
                return this.data.apiUrl + trimmedImage;
              }
              return trimmedImage;
            });
          } else {
            orderItem.medias = [];
          }
          return orderItem;
        });

        this.setData({
          orders,
          originalOrders: orders // 保存原始数据用于筛选
        });
      } else {
        console.warn('获取我的订单失败:', res.message);
        this.setData({
          orders: []
        });
      }
    }).catch(err => {
      console.error('加载我的订单失败:', err);
 
      this.setData({
        orders: []
      });
    });
  },

  // 获取商品状态显示文本
  getGoodsStatus: function (status) {
    const statusOption = this.data.statusOptions.find(option => option.nameEn === status);
    return statusOption ? statusOption.nameCn : status;
  },

  // 获取商品状态样式类名
  getGoodsStatusClass: function (status) {
    switch (status) {
      case 'list': return 'on_shelf';    // 已上架
      case 'pending': return 'pending';    // 待审核
      case 'unlist': return 'off_shelf';   // 已下架
      default: return 'off_shelf';
    }
  },

  // 获取商品类型显示名称
  getGoodsTypeName: function (type) {
    const typeOption = this.data.typeOptions.find(option => option.nameEn === type);
    return typeOption ? typeOption.nameCn : type;
  },

  // 获取商品分类显示名称
  getGoodsCategoryName: function (categoryCode) {
    const categoryOption = this.data.categoryOptions.find(option => option.nameEn === categoryCode);
    return categoryOption ? categoryOption.nameCn : categoryCode;
  },

  // 动态判断是否为免费类型（基于字典配置）
  checkIsFreeType: function (type) {
    const typeOption = this.data.typeOptions.find(option => option.nameEn === type);
    // 可以根据字典中的特殊标识来判断，比如 nameEn 包含 'free' 或者有特殊字段
    return typeOption && (typeOption.nameEn.includes('free') || typeOption.nameCn.includes('免费'));
  },

  // 获取订单状态显示名称（基于字典）
  getOrderStatusName: function (status) {
    const statusOption = this.data.orderStatusOptions.find(option => option.nameEn === status);
    return statusOption ? statusOption.nameCn : status;
  },

  // 获取订单状态样式类名
  getOrderStatusClass: function (status) {
    const statusOption = this.data.orderStatusOptions.find(option => option.nameEn === status);
    if (statusOption) {
      // 根据字典中的状态返回对应的样式类
      switch (status) {
        case 'pending': return 'pending';
        case 'wait_complete': return 'processing';
        case 'complete': return 'completed';
        case 'cancel': return 'cancelled';
        default: return 'pending';
      }
    }
    return 'pending';
  },

  // 切换订单身份（买家/卖家）
  switchOrderIdentity: function (e) {
    const identity = e.currentTarget.dataset.identity;
    this.setData({
      currentOrderIdentity: identity
    });
    // 重新加载订单数据
    this.loadOrders();
  },

  // 加载我的收藏
  loadFavorites: function () {
    return goodsApi.getMyCollectedGoodsList({
      pageNum: 1,
      pageSize: 100
    }).then(res => {
      console.log('获取我的收藏:', res);

      if (res && res.list) {
        const favorites = res.list.map(item => {

          const favoriteItem = {
            id: item.id,
            goodsId: item.goodStuffId,
            title: item.goodStuffDescribe || '', // 添加title字段用于搜索
            stuffDescribe: item.goodStuffDescribe || '',
            amount: item.amount || 0,
            image: item.media || '',
            memberName: item.goodStuffMemberName || '用户',
            // 添加卖家信息字段，尝试多个可能的字段名
            sellerId: item.goodStuffMemberId || item.memberId || item.userId || item.goodStuffUserId || item.createBy || '',
            sellerName: item.goodStuffMemberName || item.memberName || item.userName || '用户',
            favoriteTime: item.createTime ? dateUtil.formatTime(new Date(item.createTime)) : '',
            createTime: item.createTime || '', // 添加createTime字段用于排序
            type: item.goodStuffType || 'free',
            typeName: this.getGoodsTypeName(item.goodStuffType),
            isFreeType: this.checkIsFreeType(item.goodStuffType),
            categoryCode: item.goodStuffCategoryCode || '', // 添加分类字段用于筛选
            address: item.goodStuffAddress || '' // 添加地址字段用于搜索
          };

          console.log('收藏商品数据处理:', {
            原始数据: item,
            处理后: favoriteItem,
            卖家信息: {
              sellerId: favoriteItem.sellerId,
              sellerName: favoriteItem.sellerName,
              可能的ID字段: {
                goodStuffMemberId: item.goodStuffMemberId,
                memberId: item.memberId,
                userId: item.userId
              }
            }
          });
           
          // 处理图片URL
          if (favoriteItem.image) {
            favoriteItem.images = favoriteItem.image.split(',').map(image => {

              const trimmedImage = image.trim();
              if (trimmedImage && !trimmedImage.startsWith('http')) {
                return this.data.apiUrl + trimmedImage;
              }
              return trimmedImage;
            });
          } else {
            favoriteItem.images = [];
          }

          return favoriteItem;
        });



         
        this.setData({
          favorites,
          originalFavorites: favorites // 保存原始数据用于筛选
        });
      } else {
        console.warn('获取我的收藏失败:', res.message);
        this.setData({
          favorites: []
        });
      }
    }).catch(err => {
      console.error('加载我的收藏失败:', err);
   
      this.setData({
        favorites: []
      });
    });
  },

  // 导航到商品详情
  navigateToDetail: function (e) {
     
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/profilePackage/pages/goods/detail/detail?id=${id}`
    })
  },

  // 导航到发布页面
  navigateToPublish: function () {
    // 检查是否已认证
    if (util.checkAuthentication()) {
      wx.navigateTo({
        url: '/profilePackage/pages/goods/publish/publish'
      })
    } else {
      util.showAuthModal()
    }
  },

  // 导航到好物列表页面
  navigateToGoods: function () {
    wx.switchTab({
      url: '/pages/goods/goods'
    })
  },

  // 编辑商品
  editGoods: function (e) {
    const id = e.currentTarget.dataset.id;
    // 编辑商品时跳转到编辑页面，传递商品ID
    wx.navigateTo({
      url: `/pages/goods/publish/publish?id=${id}&mode=edit`
    });
  },

  // 导航到订单详情
  navigateToOrder: function (e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/profilePackage/pages/goods/order/order?id=${id}&searchType=${this.data.currentOrderIdentity}`
    })
  },

  // 导航到待核销订单列表
  navigateToPendingVerifications: function (e) {
    const goodsId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/profilePackage/pages/goods/verification/list/list?goodsId=${goodsId}`
    })
  },

  // 编辑商品
  editGoods: function (e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/profilePackage/pages/goods/publish/publish?id=${id}&edit=true`
    })
  },


  // 删除商品
  deleteGoods: function (e) {
    const id = e.currentTarget.dataset.id

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个商品吗？',
      success: (res) => {
        if (res.confirm) {
          goodsApi.deleteMyGoods(id).then(res => {
          
              // 更新本地数据
              const publishedGoods = this.data.publishedGoods.filter(item => item.id !== id);

              this.setData({
                publishedGoods
              });

              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
          
          }).catch(err => {
            console.error('删除商品失败:', err);
         
          });
        }
      }
    })
  },

  // 切换商品上架下架状态
  toggleShelfStatus: function (e) {
    const id = e.currentTarget.dataset.id
    const currentStatus = e.currentTarget.dataset.status
    const reEdit=e.currentTarget.dataset.reEdit
    // 根据当前状态确定操作和新状态
    let action, newStatus, confirmText
    if (currentStatus === 'list') {
      // 当前已上架，执行下架操作
      action = '下架'
      newStatus = 'un_list'
      confirmText = '确定要下架这个商品吗？'
    } else if (currentStatus === 'pass'|| (currentStatus==='un_list'&&!reEdit) ) {
      // 当前审核通过，执行上架操作
      action = '上架'
      newStatus = 'list'
      confirmText = '确定要上架这个商品吗？'
    } else {
      wx.showToast({
        title: '当前状态不支持此操作',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: `确认${action}`,
      content: confirmText,
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: `${action}中...`,
            mask: true
          })

          // 调用上架下架API
          goodsApi.toggleMyGoodsShelfStatus({
            id: id,
            status: newStatus
          }).then(res => {
            wx.hideLoading()

            // 更新本地数据
            const publishedGoods = this.data.publishedGoods.map(item => {
              if (item.id === id) {
                return {
                  ...item,
                  status: newStatus,
                  statusName: this.getGoodsStatus(newStatus),
                  statusClass: this.getGoodsStatusClass(newStatus)
                }
              }
              return item
            })

            this.setData({
              publishedGoods
            })

            wx.showToast({
              title: `${action}成功`,
              icon: 'success'
            })
          }).catch(err => {
            wx.hideLoading()
            console.error(`${action}商品失败:`, err)
            wx.showToast({
              title: `${action}失败`,
              icon: 'none'
            })
          })
        }
      }
    })
  },

  // 取消收藏
  cancelFavorite: function (e) {
    const id = e.currentTarget.dataset.id

    goodsApi.uncollectGoods(id).then(res => {
    
        // 更新本地数据
        const favorites = this.data.favorites.filter(item => item.id !== id);

        this.setData({
          favorites
        });

        wx.showToast({
          title: '已取消收藏',
          icon: 'success'
        });
    
    }).catch(err => {
      console.error('取消收藏失败:', err);

    });
  },

  // 取消订单
  cancelOrder: function(e) {
    const id = e.currentTarget.dataset.id
     
    // 显示取消原因选择弹窗
    this.showCancelModal(id);
  },

  // 显示取消订单弹窗
  showCancelModal: function(id) {
     
    this.setData({
      cancelOrderId:id,
      showCancelModal: true,
      selectedCancelReason: '',
      customCancelReason: ''
    });
  },
  // 隐藏取消订单弹窗
  hideCancelModal: function() {
    this.setData({
      showCancelModal: false,
      cancelOrderId:null,
    });
  },
  

  // 阻止事件冒泡
  stopPropagation: function() {
    // 空方法，用于阻止事件冒泡
  },

  // 选择取消原因
  selectCancelReason: function(e) {
    const reason = e.currentTarget.dataset.reason;
    console.log('选择预设原因:', reason);
    this.setData({
      selectedCancelReason: reason,
      customCancelReason: '' // 选择预设原因时清空自定义输入
    });
    console.log('当前选择的原因:', this.data.selectedCancelReason);
  },


  // 选择其他原因
  selectOtherReason: function() {
    console.log('选择其他原因');
    this.setData({
      selectedCancelReason: 'other'
    });
    console.log('当前选择的原因:', this.data.selectedCancelReason);
  },

  // 输入自定义取消原因
  inputCustomReason: function(e) {
    this.setData({
      customCancelReason: e.detail.value
    });
  },

  // 确认取消订单
  confirmCancelOrder: function() {
    if (!this.data.selectedCancelReason) {
      wx.showToast({
        title: '请选择取消原因',
        icon: 'none'
      });
      return;
    }

    if (this.data.selectedCancelReason === 'other' && !this.data.customCancelReason.trim()) {
      wx.showToast({
        title: '请输入取消原因',
        icon: 'none'
      });
      return;
    }

    // 获取取消原因文本
    let cancelNote = '';
    if (this.data.selectedCancelReason === 'other') {
      cancelNote = this.data.customCancelReason.trim();
    } else {
      const reasonObj = this.data.cancelReasons.find(r => r.value === this.data.selectedCancelReason);
      cancelNote = reasonObj ? reasonObj.text : '用户取消';
    }

    this.performCancelOrder(cancelNote);
  },

  // 执行取消订单操作
  performCancelOrder: function(note) {
    wx.showLoading({
      title: '处理中...'
    });
     
    // 调用取消订单API
    goodsApi.cancelMyGoodsOrder({
      id: this.data.cancelOrderId,
      note: note
    }).then(res => {
      wx.hideLoading();
    
        this.hideCancelModal()

        wx.showToast({
          title: '取消成功',
          icon: 'success'
        });
   
    }).catch(err => {
      wx.hideLoading();
      console.error('取消订单失败:', err);
   
    });
  },

  // 联系卖家 - 直接跳转到聊天页面
  contactSeller: function (e) {
    const item = e.currentTarget.dataset.order; // 这里可能是订单数据或收藏数据

    console.log('联系卖家，数据:', item);

    if (!item) {
      wx.showToast({
        title: '数据不存在',
        icon: 'none'
      });
      return;
    }

    // 提取卖家信息，支持订单和收藏两种数据结构
    let sellerId = '';
    let sellerName = '卖家';
    let goodsId = '';
    let goodsTitle = '';
    let goodsImage = '';

    // 判断是订单数据还是收藏数据
    if (item.stuffSnapshot || item.sellerId || item.orderNo) {
      // 订单数据处理
      console.log('处理订单数据');

      if (item.stuffSnapshot && item.stuffSnapshot.userId) {
        // 从交易快照中获取卖家信息
        sellerId = item.stuffSnapshot.userId;
        sellerName = item.stuffSnapshot.userName || '卖家';
        console.log('从交易快照中获取卖家信息:', {
          userId: sellerId,
          userName: sellerName
        });
      } else {
        // 兜底：从订单数据中获取
        sellerId = item.sellerId || item.userId || '';
        sellerName = item.sellerName || item.userName || '卖家';
        console.log('从订单数据中获取卖家信息:', {
          userId: sellerId,
          userName: sellerName
        });
      }

      goodsId = item.goodsId || item.id || '';
      goodsTitle = item.stuffDescribe || item.title || '';
      goodsImage = item.image || item.media || '';
    } else {
      // 收藏数据处理
      console.log('处理收藏数据');

      sellerId = item.sellerId || '';
      sellerName = item.sellerName || item.memberName || '卖家';
      goodsId = item.goodsId || item.id || '';
      goodsTitle = item.stuffDescribe || item.title || '';
      goodsImage = item.image || item.media || '';

      console.log('从收藏数据中获取卖家信息:', {
        userId: sellerId,
        userName: sellerName
      });
    }

    console.log('联系卖家参数:', {
      sellerId,
      sellerName,
      goodsId,
      goodsTitle,
      goodsImage,
      '完整数据': item
    });

    if (!sellerId) {
      wx.showToast({
        title: '卖家信息不完整',
        icon: 'none'
      });
      return;
    }

    // 直接跳转到聊天页面（传递商品信息以显示顶部商品卡片）
    wx.navigateTo({
      url: `/servicePackage/pages/messages/chat?targetId=${sellerId}&targetName=${sellerName}&goodsId=${goodsId}&goodsTitle=${goodsTitle}&goodsImage=${goodsImage}`,
      success: () => {
        console.log('跳转聊天页面成功');
      },
      fail: (err) => {
        console.error('跳转聊天页面失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },

  deleteOrder: function (e) {

    var orderId=e.currentTarget.dataset.id

    console.log('orderId',orderId)


    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          goodsApi.deleteGoodsOrder(orderId).then(res => {
           
              // 更新本地数据
              const orders = this.data.orders.filter(item => item.id !== orderId);

              this.setData({
                orders
              });

              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
        
          }).catch(err => {
            console.error('删除订单失败:', err);
       
          });
        }
      }
    })

  },


  // 预览二维码
  previewQRCode: function (e) {
    const qrCode = e.currentTarget.dataset.qrcode
    wx.previewImage({
      urls: [qrCode],
      current: qrCode
    })
  },

  // ==================== 搜索、筛选和排序功能 ====================

  // 获取搜索框提示文本
  getSearchPlaceholder: function() {
    switch(this.data.activeTab) {
      case 'published':
        return '搜索我的好物';
      case 'orders':
        return '搜索订单';
      case 'favorites':
        return '搜索收藏的好物';
      default:
        return '搜索';
    }
  },

  // 搜索输入
  onSearchInput: function (e) {
    this.setData({
      searchKeyword: e.detail.value
    });
    // 实时搜索
    this.applyFilters();
  },

  // 清除搜索
  clearSearch: function () {
    this.setData({
      searchKeyword: ''
    });
    this.applyFilters();
  },

  // 切换分类
  switchCategory: function (e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      currentCategory: category
    });
    this.applyFilters();
  },

  // 切换类型
  switchType: function (e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      currentType: type
    });
    this.applyFilters();
  },

  // 切换排序
  switchSort: function (e) {
    const sort = e.currentTarget.dataset.sort;
    this.setData({
      sortType: sort
    });
    this.applyFilters();
  },

  // 切换订单状态
  switchOrderStatus: function (e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      currentOrderStatus: status
    });
    this.applyFilters();
  },

  // 应用筛选和排序
  applyFilters: function () {
    if (this.data.activeTab === 'published') {
      this.filterPublishedGoods();
    } else if (this.data.activeTab === 'favorites') {
      this.filterFavorites();
    } else if (this.data.activeTab === 'orders') {
      this.filterOrders();
    }
  },

  // 筛选我的发布商品
  filterPublishedGoods: function () {
    let filteredGoods = [...this.data.originalPublishedGoods];

    // 搜索筛选
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      filteredGoods = filteredGoods.filter(item => {
        const title = (item.title || item.stuffDescribe || '').toLowerCase();
        const address = (item.address || '').toLowerCase();
        return title.includes(keyword) || address.includes(keyword);
      });
    }

    // 分类筛选
    if (this.data.currentCategory !== 'all') {
      filteredGoods = filteredGoods.filter(item => item.categoryCode === this.data.currentCategory);
    }

    // 类型筛选
    if (this.data.currentType !== 'all') {
      filteredGoods = filteredGoods.filter(item => item.type === this.data.currentType);
    }

    // 排序
    this.sortGoods(filteredGoods);

    this.setData({
      publishedGoods: filteredGoods
    });
  },

  // 筛选收藏商品
  filterFavorites: function () {
    let filteredFavorites = [...this.data.originalFavorites];

    // 搜索筛选
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      filteredFavorites = filteredFavorites.filter(item => {
        const title = (item.title || item.stuffDescribe || '').toLowerCase();
        const address = (item.address || '').toLowerCase();
        return title.includes(keyword) || address.includes(keyword);
      });
    }

    // 分类筛选
    if (this.data.currentCategory !== 'all') {
      filteredFavorites = filteredFavorites.filter(item => item.categoryCode === this.data.currentCategory);
    }

    // 类型筛选
    if (this.data.currentType !== 'all') {
      filteredFavorites = filteredFavorites.filter(item => item.type === this.data.currentType);
    }

    // 排序
    this.sortGoods(filteredFavorites);

    this.setData({
      favorites: filteredFavorites
    });
  },

  // 商品排序
  sortGoods: function (goods) {
    switch (this.data.sortType) {
      case 'newest':
        goods.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
        break;
      case 'amount-asc':
        goods.sort((a, b) => (a.amount || a.totalAmount || 0) - (b.amount || b.totalAmount || 0));
        break;
      case 'amount-desc':
        goods.sort((a, b) => (b.amount || b.totalAmount || 0) - (a.amount || a.totalAmount || 0));
        break;
      default:
        // 默认按创建时间排序
        goods.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
        break;
    }
  },

  // 筛选订单
  filterOrders: function () {
    let filteredOrders = [...this.data.originalOrders];

    // 搜索筛选
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      filteredOrders = filteredOrders.filter(item => {
        const orderNo = (item.orderNo || '').toLowerCase();
        const goodsTitle = (item.goodsTitle || item.stuffDescribe || '').toLowerCase();
        const buyerName = (item.buyerName || '').toLowerCase();
        const sellerName = (item.sellerName || '').toLowerCase();
        return orderNo.includes(keyword) ||
               goodsTitle.includes(keyword) ||
               buyerName.includes(keyword) ||
               sellerName.includes(keyword);
      });
    }

    // 分类筛选
    if (this.data.currentCategory !== 'all') {
      filteredOrders = filteredOrders.filter(item => item.categoryCode === this.data.currentCategory);
    }

    // 类型筛选
    if (this.data.currentType !== 'all') {
      filteredOrders = filteredOrders.filter(item => item.type === this.data.currentType);
    }

    // 订单状态筛选
    if (this.data.currentOrderStatus !== 'all') {
      filteredOrders = filteredOrders.filter(item => item.status === this.data.currentOrderStatus);
    }

    // 排序
    this.sortGoods(filteredOrders);

    this.setData({
      orders: filteredOrders
    });
  }
})
