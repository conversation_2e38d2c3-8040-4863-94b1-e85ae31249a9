# 工单列表页搜索和筛选功能优化

## 修改概述

对 `propertyPackage/pages/property/workorder/list/index` 工单列表页进行了搜索和筛选功能的优化，主要包括：

1. 将筛选按钮从搜索框右侧移动到工具栏（原批量操作位置）
2. 暂时隐藏批量操作功能
3. 在原筛选位置添加清除所有筛选条件的按钮
4. 为搜索框添加实时搜索功能（输入监听）
5. 优化用户体验和界面布局

## 详细修改内容

### 1. WXML 结构调整

#### 1.1 搜索栏修改
```xml
<!-- 修改前 -->
<view class="search-input-wrapper">
  <view class="search-icon"></view>
  <input class="search-input" placeholder="搜索工单号、关键词" value="{{searchValue}}" confirm-type="search" bindconfirm="onSearch" />
  <view class="clear-icon" wx:if="{{searchValue}}" bindtap="clearSearch"></view>
  <view class="filter-button-inline" bindtap="toggleFilterPanel">
    <view class="filter-icon"></view>
    <text>筛选</text>
  </view>
</view>

<!-- 修改后 -->
<view class="search-input-wrapper">
  <view class="search-icon"></view>
  <input class="search-input" placeholder="搜索工单号、关键词" value="{{searchValue}}" confirm-type="search" bindconfirm="onSearch" bindinput="onSearchInput" />
  <view class="clear-search-button" wx:if="{{searchValue || currentStatus || currentType || currentSort !== 'time-desc'}}" bindtap="clearAllFilters">
    <view class="clear-icon"></view>
  </view>
</view>
```

#### 1.2 工具栏修改
```xml
<!-- 修改前 -->
<view class="toolbar-actions">
  <view class="action-button {{isSelectMode ? 'active' : ''}}" bindtap="toggleSelectMode">
    <text>{{isSelectMode ? '取消' : '批量操作'}}</text>
    <view class="selected-count" wx:if="{{isSelectMode && selectedOrders.length > 0}}">{{selectedOrders.length}}</view>
  </view>
</view>

<!-- 修改后 -->
<view class="toolbar-actions">
  <!-- 筛选按钮 -->
  <view class="action-button filter-button" bindtap="toggleFilterPanel">
    <view class="filter-icon"></view>
    <text>筛选</text>
    <view class="filter-badge" wx:if="{{hasActiveFilters}}"></view>
  </view>
  <!-- 批量操作按钮（暂时隐藏） -->
  <view class="action-button {{isSelectMode ? 'active' : ''}}" bindtap="toggleSelectMode" hidden="{{true}}">
    <text>{{isSelectMode ? '取消' : '批量操作'}}</text>
    <view class="selected-count" wx:if="{{isSelectMode && selectedOrders.length > 0}}">{{selectedOrders.length}}</view>
  </view>
</view>
```

### 2. JavaScript 功能增强

#### 2.1 新增数据字段
```javascript
data: {
  // 筛选状态
  hasActiveFilters: false,
  // 搜索防抖定时器
  searchTimer: null
}
```

#### 2.2 实时搜索功能
```javascript
// 搜索输入监听（实时搜索）
onSearchInput: function(e) {
  const searchValue = e.detail.value.trim();
  
  // 更新搜索值
  this.setData({
    searchValue: searchValue
  });

  // 清除之前的定时器
  if (this.data.searchTimer) {
    clearTimeout(this.data.searchTimer);
  }

  // 设置防抖定时器，500ms后执行搜索
  const timer = setTimeout(() => {
    this.performSearch(searchValue);
  }, 500);

  this.setData({
    searchTimer: timer
  });

  // 更新筛选状态
  this.updateFilterStatus();
}
```

#### 2.3 清除所有筛选功能
```javascript
// 清除所有筛选条件
clearAllFilters: function() {
  console.log('清除所有筛选条件');
  
  // 清除搜索定时器
  if (this.data.searchTimer) {
    clearTimeout(this.data.searchTimer);
  }

  this.setData({
    searchValue: '',
    currentStatus: '',
    currentType: '',
    currentSort: 'time-desc',
    currentPage: 1,
    orders: [],
    filteredOrders: [],
    searchTimer: null
  });
  
  this.loadWorkOrders();
  this.updateFilterStatus();
  
  wx.showToast({
    title: '已清除筛选',
    icon: 'success',
    duration: 1500
  });
}
```

#### 2.4 筛选状态管理
```javascript
// 更新筛选状态
updateFilterStatus: function() {
  const hasActiveFilters = !!(
    this.data.searchValue ||
    this.data.currentStatus ||
    this.data.currentType ||
    this.data.currentSort !== 'time-desc'
  );
  
  this.setData({
    hasActiveFilters: hasActiveFilters
  });
}
```

### 3. CSS 样式优化

#### 3.1 清除按钮样式
```css
/* 清除搜索按钮 */
.clear-search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  margin-left: 16rpx;
  border-radius: 30rpx;
  background-color: #f0f0f0;
  transition: all 0.3s ease;
}

.clear-search-button:active {
  background-color: #e0e0e0;
  transform: scale(0.95);
}
```

#### 3.2 筛选按钮样式
```css
/* 筛选按钮特定样式 */
.action-button.filter-button {
  position: relative;
}

.action-button.filter-button .filter-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  background-image: url("data:image/svg+xml,...");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

/* 筛选状态指示器 */
.filter-badge {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 16rpx;
  height: 16rpx;
  background-color: #ff4757;
  border-radius: 50%;
  border: 2rpx solid #fff;
}
```

## 功能特点

### 1. 实时搜索
- **防抖机制**：输入停止500ms后自动执行搜索
- **即时反馈**：输入过程中实时更新搜索值
- **性能优化**：避免频繁的API调用

### 2. 智能清除
- **条件检测**：只有在有筛选条件时才显示清除按钮
- **一键清除**：清除所有筛选条件（搜索、状态、类型、排序）
- **用户反馈**：清除后显示成功提示

### 3. 筛选状态指示
- **视觉提示**：有活动筛选条件时显示红色圆点
- **状态同步**：实时更新筛选状态
- **用户体验**：清晰的视觉反馈

### 4. 界面优化
- **布局调整**：筛选按钮移至更合理的位置
- **功能隐藏**：暂时隐藏批量操作，简化界面
- **样式统一**：保持与整体设计风格一致

## 使用方法

### 1. 实时搜索
用户在搜索框中输入关键词，系统会在停止输入500ms后自动执行搜索。

### 2. 筛选功能
点击工具栏右侧的"筛选"按钮，打开筛选面板进行条件筛选。

### 3. 清除筛选
当有任何筛选条件时，搜索框右侧会显示清除按钮，点击可一键清除所有筛选条件。

### 4. 筛选状态
筛选按钮上的红色圆点表示当前有活动的筛选条件。

## 技术要点

### 1. 防抖处理
使用 `setTimeout` 实现搜索防抖，避免频繁的API调用。

### 2. 状态管理
通过 `updateFilterStatus` 方法统一管理筛选状态。

### 3. 内存管理
在页面卸载时清除定时器，避免内存泄漏。

### 4. 用户体验
提供清晰的视觉反馈和操作提示。

## 注意事项

1. **批量操作功能**：当前已隐藏，如需恢复可移除 `hidden="{{true}}"` 属性
2. **搜索性能**：防抖时间设置为500ms，可根据实际需求调整
3. **筛选条件**：清除功能会重置所有筛选条件到默认状态
4. **兼容性**：保持了原有功能的完整性，只是调整了界面布局
