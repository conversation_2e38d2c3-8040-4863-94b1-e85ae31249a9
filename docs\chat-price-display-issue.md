# 聊天页面价格显示和数据丢失问题分析

## 问题描述

1. **第一次点击联系卖家**：金额没有显示（显示为 ¥ 但没有具体数字）
2. **返回后再次点击**：提示"卖家信息不完整"

## 问题分析

### 1. 金额没有显示的原因

**根本原因**：从收藏列表跳转到聊天页面时，没有传递 `price` 参数。

#### 代码分析

```javascript
// 修复前：没有传递price参数
wx.navigateTo({
  url: `/servicePackage/pages/messages/chat?targetId=${sellerId}&targetName=${sellerName}&goodsId=${goodsId}&goodsTitle=${goodsTitle}&goodsImage=${goodsImage}`
});

// 聊天页面接收参数
const { targetId, targetName, goodsId, targetAvatar, goodsTitle, goodsImage, price, title } = options

// 设置商品信息时使用price
this.setData({
  goodsInfo: {
    id: goodsId,
    title: decodedGoodsTitle,
    price: price, // 如果没有传递price，这里就是undefined
    image: decodedGoodsImage
  }
});
```

#### 修复方案

```javascript
// 修复后：传递price参数
const goodsPrice = item.amount || 0;

wx.navigateTo({
  url: `/servicePackage/pages/messages/chat?targetId=${sellerId}&targetName=${sellerName}&goodsId=${goodsId}&goodsTitle=${goodsTitle}&goodsImage=${goodsImage}&price=${goodsPrice}`
});
```

### 2. 第二次提示"卖家信息不完整"的原因

**可能的原因分析**：

#### 原因1：页面生命周期导致数据重置

```javascript
onShow: function () {
  // 每次显示页面时刷新数据
  this.loadData()
},
```

当从聊天页面返回时，会触发 `onShow` 方法，重新加载数据。如果在数据加载过程中用户立即点击联系卖家，可能会获取到空数据。

#### 原因2：数据绑定问题

WXML中的数据绑定可能在某些情况下失效：

```xml
<!-- 可能的问题：data-order绑定失效 -->
<view class="action-btn contact" bindtap="contactSeller" data-order="{{item}}">联系卖家</view>
```

#### 原因3：异步数据加载时序问题

如果用户在数据还没有完全加载完成时点击按钮，可能会获取到不完整的数据。

## 调试方案

### 1. 添加详细的调试信息

```javascript
contactSeller: function (e) {
  const item = e.currentTarget.dataset.order;
  
  console.log('=== 联系卖家调试信息 ===');
  console.log('获取到的item数据:', item);
  console.log('item类型:', typeof item);
  console.log('item的关键字段:', {
    id: item.id,
    userId: item.userId,
    userName: item.userName,
    stuffSnapshot: item.stuffSnapshot,
    amount: item.amount
  });
  
  if (!item) {
    console.error('数据不存在，可能的原因：');
    console.error('1. WXML中data-order没有正确绑定');
    console.error('2. 页面数据被清空');
    console.error('3. 事件绑定有问题');
    return;
  }
  
  // ... 后续处理
}
```

### 2. 检查数据完整性

```javascript
// 在处理收藏数据时，检查关键字段
if (!item.userId) {
  console.error('收藏数据中缺少userId字段:', item);
  wx.showToast({
    title: '商品数据不完整',
    icon: 'none'
  });
  return;
}
```

## 修复方案

### 1. 修复价格显示问题

```javascript
// profilePackage/pages/goods/my/my.js
contactSeller: function (e) {
  // ... 其他逻辑
  
  // 获取商品价格
  const goodsPrice = item.amount || 0;

  // 跳转时传递价格参数
  wx.navigateTo({
    url: `/servicePackage/pages/messages/chat?targetId=${sellerId}&targetName=${sellerName}&goodsId=${goodsId}&goodsTitle=${goodsTitle}&goodsImage=${goodsImage}&price=${goodsPrice}`
  });
}
```

### 2. 防止数据丢失

#### 方案A：优化onShow逻辑

```javascript
onShow: function () {
  // 只在必要时刷新数据，避免频繁重载
  if (!this.data.publishedGoods.length && !this.data.orders.length && !this.data.favorites.length) {
    this.loadData();
  }
}
```

#### 方案B：添加数据验证

```javascript
contactSeller: function (e) {
  const item = e.currentTarget.dataset.order;
  
  // 数据验证
  if (!item || typeof item !== 'object') {
    console.error('数据异常:', item);
    wx.showToast({
      title: '请稍后重试',
      icon: 'none'
    });
    return;
  }
  
  // ... 后续处理
}
```

#### 方案C：使用防抖处理

```javascript
contactSeller: function (e) {
  // 防止快速重复点击
  if (this.contactSellerLock) {
    return;
  }
  this.contactSellerLock = true;
  
  setTimeout(() => {
    this.contactSellerLock = false;
  }, 1000);
  
  // ... 后续处理
}
```

## 测试验证

### 1. 价格显示测试

1. 进入我的收藏列表
2. 点击任意商品的"联系卖家"
3. 检查聊天页面顶部商品卡片是否显示正确的价格

### 2. 数据持久性测试

1. 进入我的收藏列表
2. 点击"联系卖家"进入聊天页面
3. 点击左上角返回到收藏列表
4. 立即再次点击"联系卖家"
5. 检查是否正常跳转，不出现"卖家信息不完整"提示

### 3. 边界情况测试

1. 在数据加载过程中快速点击"联系卖家"
2. 快速连续点击"联系卖家"按钮
3. 在网络较慢的情况下测试

## 预期效果

修复后应该达到以下效果：

1. **价格正确显示**：聊天页面顶部商品卡片显示正确的价格
2. **数据稳定性**：返回后再次点击不会出现"卖家信息不完整"
3. **用户体验**：操作流畅，没有异常提示

## 修改的文件

1. **`profilePackage/pages/goods/my/my.js`**
   - 添加price参数传递
   - 增强调试信息
   - 移除debugger语句

## 后续优化建议

1. **数据缓存**：考虑在页面级别缓存数据，减少不必要的重新加载
2. **错误处理**：完善错误处理机制，提供更友好的用户提示
3. **性能优化**：优化数据加载逻辑，避免频繁的API调用
4. **用户反馈**：在数据加载时显示loading状态，提升用户体验
