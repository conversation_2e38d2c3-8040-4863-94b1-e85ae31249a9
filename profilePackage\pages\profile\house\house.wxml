<!-- 我的房屋页面 -->
<view class="container">
  <!-- 内容区域 -->
  <view class="content-area">
    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading}}">
      <view class="loading-text">正在加载房屋列表...</view>
    </view>

    <!-- 查找我的房产按钮 -->
    <view wx:if="{{houses.length == 0}}" class="search-myhouse" bindtap="checkRealPersonHouse">
      查找我的房产
      <image class="search-icon" src="/images/icons/search.svg" mode="aspectFit"></image>
    </view>

    <!-- 房屋列表 -->
    <block wx:if="{{!loading && houses.length > 0}}">
      <view class="house-count">共 {{total > 0 ? total : houses.length}} 套房屋</view>
      <view class="house-list">
        <view class="house-card {{item.isDefault ? 'default' : ''}}"
              wx:for="{{houses}}"
              wx:key="id"
              bindtap="goToHouseDetail"
              data-id="{{item.id}}">
          <view class="house-content">
            <view class="house-address">{{item.fullAddress}}</view>
            <view class="house-info">
              <view class="house-role">{{item.roleText}}</view>
              <view class="house-status">
                <text class="status-tag tag-default" wx:if="{{item.isDefault}}">默认</text>
                <view class="status-tag " style="background-color: {{item.statusClass}}40;;color:  {{item.statusClass}};"  >
                  {{item.statusText}}
                </view>
              </view>
            </view>
          </view>

        </view>
      </view>

      <!-- 加载更多状态 -->
      <view class="load-more-container">
        <view class="load-more-text" wx:if="{{loadingMore}}">正在加载更多...</view>
        <view class="load-more-text" wx:elif="{{!hasMore && total > pageSize}}">已加载全部数据</view>
      </view>
    </block>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && houses.length === 0}}">
      <view class="empty-icon">
        <!-- 使用图片代替SVG -->
        <view class="house-icon"></view>
      </view>
      <view class="empty-text">您还没有添加房屋信息</view>
      <view class="empty-desc">点击右下角按钮添加房屋</view>
    </view>
  </view>

  <!-- 悬浮添加按钮 -->
  <view class="floating-add-button" bindtap="showAddHouseModal">
    <view class="add-icon">+</view>
  </view>

  <!-- 新增房屋弹窗 -->
  <view class="add-house-modal {{showAddHouseModal ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="modal-mask" bindtap="hideAddHouseModal"></view>
    <view class="modal-content">
      <!-- 弹窗头部 -->
      <view class="modal-header">
        <text class="modal-title">新增房屋</text>
     
        <image src="/images/icons/close.svg" mode="aspectFit" bindtap="hideAddHouseModal"  class="modal-close"></image>
      </view>

      <!-- 已选择信息显示 -->
      <view class="selection-info" wx:if="{{selectedBuildingName || selectedUnitNumber || selectedRoomName}}">
        <view class="selection-item-info" wx:if="{{selectedBuildingName}}">
          <text class="selection-label">已选楼栋：</text>
          <text class="selection-value">{{selectedBuildingName}}</text>
          <view class="back-button" wx:if="{{currentStep === 'unit' || currentStep === 'room'}}" bindtap="backToBuilding">
            <text class="back-text">重新选择</text>
          </view>
        </view>
        <view class="selection-item-info" wx:if="{{selectedUnitNumber}}">
          <text class="selection-label">已选单元：</text>
          <text class="selection-value">{{selectedUnitNumber}}</text>
          <view class="back-button" wx:if="{{currentStep === 'room'}}" bindtap="backToUnit">
            <text class="back-text">重新选择</text>
          </view>
        </view>
        <view class="selection-item-info" wx:if="{{selectedRoomName}}">
          <text class="selection-label">已选房间：</text>
          <text class="selection-value">{{selectedRoomName}}</text>
        </view>
      </view>

      <!-- 弹窗内容 -->
      <view class="modal-body">
        <!-- 搜索框 -->
        <view class="search-container">
          <input
            class="search-input"
            placeholder="{{searchPlaceholder}}"
            value="{{searchKeyword}}"
            bindinput="onSearchInput"
            placeholder-style="color: #999;"
          />
        </view>

        <!-- 动态内容区域 -->
        <scroll-view scroll-y="true" class="content-scroll">
          <!-- 楼栋列表 -->
          <view wx:if="{{currentStep === 'building'}}">
            <view class="selection-grid" wx:if="{{filteredBuildings.length > 0}}">
              <view
                class="selection-item {{item.id === selectedBuildingId ? 'selected' : ''}}"
                wx:for="{{filteredBuildings}}"
                wx:key="id"
                bindtap="selectBuilding"
                data-id="{{item.id}}"
                data-name="{{item.buildingNumber}}"
              >
                {{item.buildingNumber}}
              </view>
            </view>
            <!-- 楼栋空状态 -->
            <view class="empty-state-small" wx:if="{{filteredBuildings.length === 0 && buildings.length === 0}}">
              <text class="empty-text-small">暂无楼栋信息</text>
            </view>
            <view class="empty-state-small" wx:if="{{filteredBuildings.length === 0 && buildings.length > 0}}">
              <text class="empty-text-small">未找到匹配的楼栋</text>
            </view>
          </view>

          <!-- 单元/房间混合列表 -->
          <view wx:if="{{currentStep === 'unit'}}">
            <view class="selection-grid" wx:if="{{filteredUnits.length > 0}}">
              <view
                class="selection-item {{(item.type === 'unit' && item.unitNumber === selectedUnitNumber) || (item.type === 'room' && item.id === selectedRoomId) ? 'selected' : ''}}"
                wx:for="{{filteredUnits}}"
                wx:key="*this"
                bindtap="selectUnit"
                data-unit="{{item.type === 'unit' ? item.unitNumber : ''}}"
                data-room-id="{{item.type === 'room' ? item.id : ''}}"
                data-room-name="{{item.type === 'room' ? item.roomNumber : ''}}"
              >
                {{item.displayName}}
              </view>
            </view>
            <!-- 单元/房间空状态 -->
            <view class="empty-state-small" wx:if="{{filteredUnits.length === 0 && units.length === 0}}">
              <text class="empty-text-small">该楼栋暂无单元/房间信息</text>
            </view>
            <view class="empty-state-small" wx:if="{{filteredUnits.length === 0 && units.length > 0}}">
              <text class="empty-text-small">未找到匹配的单元/房间</text>
            </view>
          </view>

          <!-- 房间列表 -->
          <view wx:if="{{currentStep === 'room'}}">
            <view class="selection-grid" wx:if="{{filteredRooms.length > 0}}">
              <view
                class="selection-item {{item.id === selectedRoomId ? 'selected' : ''}}"
                wx:for="{{filteredRooms}}"
                wx:key="id"
                bindtap="selectRoom"
                data-id="{{item.id}}"
                data-name="{{item.roomNumber}}"
              >
                {{item.roomNumber}}
              </view>
            </view>
            <!-- 房间空状态 -->
            <view class="empty-state-small" wx:if="{{filteredRooms.length === 0 && rooms.length === 0}}">
              <text class="empty-text-small">该{{selectedUnitNumber ? '单元' : '楼栋'}}暂无房间信息</text>
            </view>
            <view class="empty-state-small" wx:if="{{filteredRooms.length === 0 && rooms.length > 0}}">
              <text class="empty-text-small">未找到匹配的房间</text>
            </view>
          </view>
        </scroll-view>

        <!-- 住户身份选择区域 - 隐藏但保留逻辑 -->
        <view class="resident-type-section" >
          <view class="section-title">选择住户身份</view>
          <view class="resident-type-grid">
            <view
              class="resident-type-item {{item.nameEn === selectedResidentType ? 'selected' : ''}}"
              wx:for="{{residentOptionsAddWindow}}"
              wx:key="nameEn"
              bindtap="selectResidentType"
              data-type="{{item.nameEn}}"
            >
              {{item.nameCn}}
            </view>
          </view>
        </view>
      </view>

      <!-- 弹窗底部操作栏 -->
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideAddHouseModal">取消</button>
        <button
          class="btn-confirm {{canSubmit ? 'enabled' : 'disabled'}}"
          bindtap="submitAddHouse"
          disabled="{{!canSubmit}}"
        >
          确定
        </button>
      </view>
    </view>
  </view>

  <!-- 查找我的房产弹窗 -->
  <view class="find-house-modal {{showFindHouseModal ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="modal-mask" bindtap="hideFindHouseModal"></view>
    <view class="modal-content find-house-content">
      <!-- 弹窗头部 -->
      <view class="modal-header">
        <view class="modal-title">绑定住户</view>
        <view class="modal-close" bindtap="hideFindHouseModal">
          <text class="close-icon">×</text>
        </view>
      </view>

      <!-- 房产信息区域 -->
      <view class="resident-info-section" wx:if="{{realPersonData}}">
        <view class="info-row">
          <text class="info-label">住户姓名</text>
          <text class="info-value">{{realPersonData.residentName}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">手机号码</text>
          <text class="info-value">{{realPersonData.phone }}</text>
        </view>
      </view>

      <!-- 住户列表区域 -->
      <view class="modal-body find-house-body">
        <!-- 加载状态 -->
        <view class="loading-container" wx:if="{{findHouseLoading}}">
          <view class="loading-spinner">
            <view class="spinner"></view>
          </view>
          <view class="loading-text">正在获取住户信息...</view>
        </view>

        <!-- 房产列表表格 -->
        <view class="house-table-container" wx:if="{{!findHouseLoading && realPersonData && realPersonData.roomList && realPersonData.roomList.length > 0}}">
          <!-- 表格头部 -->
          <view class="table-header">
            <view class="table-cell header-cell building-col">楼栋</view>
            <view class="table-cell header-cell unit-col">单元</view>
            <view class="table-cell header-cell room-col">房号</view>
            <view class="table-cell header-cell type-col">住户类型</view>
            <view class="table-cell header-cell status-col">状态</view>
          </view>

          <!-- 表格内容 -->
          <scroll-view scroll-y="true" class="table-scroll">
            <view class="table-row"
                  wx:for="{{realPersonData.roomList}}"
                  wx:key="id">
              <view class="table-cell building-col">{{item.buildingNumber || '-'}}</view>
              <view class="table-cell unit-col">{{item.unitNumber || '-'}}</view>
              <view class="table-cell room-col">{{item.roomNumber || '-'}}</view>
              <view class="table-cell type-col">
                <view class="type-tag owner">{{item.residentTypeText || '业主'}}</view>
              </view>
              <view class="table-cell status-col">
                <view class="status-tag active">正常</view>
              </view>
            </view>
          </scroll-view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state-modal" wx:if="{{!findHouseLoading && (!realPersonData || !realPersonData.roomList || realPersonData.roomList.length === 0)}}">
          <view class="empty-icon-modal">🏠</view>
          <view class="empty-text-modal">暂无可绑定的房产</view>
          <view class="empty-desc-modal">请联系物业管理员添加房产信息</view>
        </view>
      </view>

      <!-- 弹窗底部操作栏 -->
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideFindHouseModal">取消</button>
        <button
          class="btn-confirm enabled"
          bindtap="bindResident"
          loading="{{bindingHouses}}"
        >
          {{bindingHouses ? '绑定中...' : '绑定住户'}}
        </button>
      </view>
    </view>
  </view>



</view>
