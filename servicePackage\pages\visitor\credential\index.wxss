/* 引入访客公共样式 */
@import "/styles/visitor-common.wxss";

/* 访客凭证页特定样式 */
.visitor-credential-container {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  width:calc(100% - 64rpx);
}

.visitor-credential-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  
}

.visitor-countdown {
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.visitor-countdown.active {
  background-color: #fff3e0;
  border: 1px solid #ffb74d;
  color: #e65100;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 183, 77, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(255, 183, 77, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 183, 77, 0);
  }
}

.countdown-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.visitor-community-name {
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 16px;
}

.visitor-type {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.visitor-type-icon-container {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.visitor-car-number-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16px;
}

.visitor-car-number {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  border: 1px solid #0d47a1;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.car-number-prefix {
  background-color: #0d47a1;
  color: white;
  padding: 8px 12px;
  font-size: 18px;
  font-weight: 600;
}

.car-number-content {
  background-color: white;
  color: #0d47a1;
  padding: 8px 12px;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 2px;
}

.car-number-label {
  font-size: 12px;
  color: #666;
}

.visitor-credential-info-card {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.visitor-card-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f9fafb;
  border-bottom: 1px solid #eee;
}

.visitor-card-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.visitor-card-title {
  font-size: 16px;
  font-weight: 500;
}

.visitor-card-header-left {
  display: flex;
  align-items: center;
}

.visitor-card-header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 收藏图标样式 */
.favorite-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  background-color: transparent;
}

.favorite-icon .star-icon {
  width: 16px;
  height: 16px;
  opacity: 0.4;
  transition: all 0.3s ease;
}

.favorite-icon.active {
  background-color: #ffd700;
  border-radius: 50%;
}

.favorite-icon.active .star-icon {
  opacity: 1;
  filter: brightness(0) invert(1);
}

.favorite-icon:active {
  transform: scale(0.9);
}

.visitor-card-content {
  padding: 16px;
}

.visitor-info-row {
  display: flex;
  margin-bottom: 8px;
}

.visitor-info-row:last-child {
  margin-bottom: 0;
}

.visitor-info-label {
  width: 80px;
  color: #666;
  font-size: 14px;
}

.visitor-info-value {
  flex: 1;
  font-size: 14px;
}

.visitor-qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
}

.visitor-qrcode-wrapper {
  width: 200px;
  height: 200px;
  margin-bottom: 16px;
  position: relative;

}

.visitor-qrcode-canvas {
  width: 100%;
  height: 100%;
}

.visitor-qrcode-logo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  background-color: #fff;
  border-radius: 20px;
  padding: 5px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

.visitor-current-time {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

/* 失效提示样式 */
.visitor-expired-tip {
  font-size: 16px;
  color: #ff4444;
  font-weight: bold;
  text-align: center;
  margin-bottom: 12px;
  padding: 8px 16px;
  background-color: rgba(255, 68, 68, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 68, 68, 0.3);
}

/* 二维码失效占位样式 */
.qrcode-expired-placeholder {
  width: 380rpx;
  height: 380rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  border: 2rpx dashed #ddd;
}

.expired-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.expired-text {
  font-size: 28rpx;
  color: #999;
  font-weight: 500;
}

.visitor-usage-tip {
  font-size: 14px;
  text-align: center;
  padding: 8px 16px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
}

.visitor-usage-tip.smart {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #a5d6a7;
}

.visitor-usage-tip.normal {
  background-color: #e3f2fd;
  color: #1565c0;
  border: 1px solid #90caf9;
}

.usage-tip-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

/* 核销按钮区域样式 */
.visitor-verify-section {
  margin: 24px 0;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.visitor-verify-btn {
  width: 200px;
  height: 50px;
  background-color: #fff;
  color: #667eea;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  border: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 12px;
}

.visitor-verify-btn:not(.verifying):active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.visitor-verify-btn.verifying {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.verify-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.verify-icon.loading {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.verify-tip {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  text-align: center;
}

.verify-success {
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}

.success-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

/* 操作图标区域样式 */
.visitor-action-icons {
  margin-top: 24px;
  padding: 16px 0;
  border-top: 1px dashed #eee;
}

.action-icon-row {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.action-icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 16px;
}

.action-icon-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.action-icon-circle:active {
  transform: scale(0.95);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.action-icon {
  width: 24px;
  height: 24px;
}

.action-icon-text {
  font-size: 12px;
  color: #666;
}

/* 不同操作图标的颜色 */
.action-icon-circle.save {
  background-color: #f0f4ff;
  border: 1px solid #d6e4ff;
}

.action-icon-circle.share {
  background-color: #e6f7ef;
  border: 1px solid #c3e6d4;
}

.action-icon-circle.cancel {
  background-color: #fff1f0;
  border: 1px solid #ffccc7;
}

.action-icon-circle.extend {
  background-color: #fff7e6;
  border: 1px solid #ffe7ba;
}

.action-icon-circle.activate {
  background-color: #e6f7ff;
  border: 1px solid #bae7ff;
}

.visitor-save-btn {
  color: #4f46e5;
}

.visitor-share-btn {
  color: #10b981;
}

.visitor-extend-btn {
  color: #f59e0b;
}

.visitor-activate-btn {
  color: #0ea5e9;
}

.visitor-cancel-btn {
  color: #ef4444;
}

/* 分享选项样式 */
.visitor-share-options {
  display: flex;
  justify-content: space-around;
  padding: 16px 0;
}

.visitor-share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.visitor-share-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.visitor-share-icon.wechat {
  background-color: #07c160;
}

.visitor-share-icon.link {
  background-color: #4f46e5;
}

.visitor-option-icon {
  width: 30px;
  height: 30px;
}

.visitor-option-name {
  font-size: 12px;
  color: #333;
}

/* 延期选项样式 */
.visitor-modal-body {
  padding: 16px;
}

.extend-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 16px;
}

.extend-option {
  width: 48%;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.extend-option.active {
  background-color: #f59e0b;
  color: white;
}

.extend-option-value {
  font-size: 16px;
  font-weight: 500;
}

.extend-info {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  color: #666;
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 8px;
}

.extend-info text {
  margin-bottom: 4px;
}

.visitor-modal-footer {
  display: flex;
  padding: 16px;
  border-top: 1px solid #eee;
}

.visitor-modal-btn {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin: 0 8px;
}

.visitor-modal-cancel-btn {
  background-color: #f5f5f5;
  color: #333;
}

.visitor-modal-confirm-btn {
  background-color: #f59e0b;
  color: white;
}

.visitor-modal-confirm-btn.danger {
  background-color: #ef4444;
  color: white;
}

/* 作废弹窗样式 */
.visitor-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.visitor-modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.visitor-modal {
  background-color: white;
  border-radius: 12px;
  width: 80%;
  max-width: 400px;
  overflow: hidden;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.visitor-modal-overlay.show .visitor-modal {
  transform: scale(1);
}

.visitor-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.visitor-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.visitor-modal-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 20px;
  cursor: pointer;
}

.visitor-modal-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.warning-text {
  color: #ef4444;
  font-size: 14px;
}

/* 网络错误提示样式 */
.network-error-toast {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.network-error-toast.show {
  opacity: 1;
  visibility: visible;
}

.network-error-content {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80%;
  max-width: 300px;
}

.error-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
}

.network-error-content text {
  font-size: 16px;
  color: #333;
  text-align: center;
  margin-bottom: 16px;
}

.retry-btn {
  background-color: #4f46e5;
  color: white;
  border-radius: 8px;
  width: 120px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}
