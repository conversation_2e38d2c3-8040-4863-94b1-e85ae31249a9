
const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

// 获取问候语
const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 6) return '凌晨好'
  if (hour < 9) return '早上好'
  if (hour < 12) return '上午好'
  if (hour < 14) return '中午好'
  if (hour < 18) return '下午好'
  if (hour < 22) return '晚上好'
  return '夜深了'
}

// 模拟获取天气数据
const getWeatherData = () => {
  // 天气类型：晴、多云、雨、雪
  const conditions = ['sunny', 'cloudy', 'rainy', 'snowy']
  const temps = ['23°C', '18°C', '15°C', '2°C']

  // 随机选择一种天气（实际项目中应根据真实数据）
  const index = Math.floor(Math.random() * conditions.length)

  return {
    condition: conditions[index],
    temperature: temps[index]
  }
}

// 获取天气心情语
const getWeatherMood = (condition) => {
  switch (condition) {
    case 'sunny':
      return '阳光明媚，愿您的心情如此灿烂'
    case 'cloudy':
      return '今天云朵飘飘，愿您的生活轻松愉快'
    case 'rainy':
      return '雨水滋润万物，愿好运滋润您的生活'
    case 'snowy':
      return '雪花纷飞，愿您的生活充满温暖与惊喜'
    default:
      return '愿您今天开心愉快'
  }
}

// 检查用户是否已认证
const checkAuthentication = () => {
  const userInfo = wx.getStorageSync('userInfo')
  if (userInfo && userInfo.role == 'user') {
    wx.setStorageSync('isAuthenticated', true)
    return true
  } else {
    wx.setStorageSync('isAuthenticated', true)
    return false
  }
}

// 显示认证提醒弹窗
const showAuthModal = (pageToNavigate) => {
  // 检查用户是否已认证
  if (checkAuthentication()) {
    // 已认证，直接跳转到目标页面
    if (pageToNavigate) {
      wx.navigateTo({
        url: pageToNavigate
      })
    }
    return true
  } else {
    // 未认证，显示认证提醒弹窗
    wx.showModal({
      title: '需要身份认证',
      content: '您当前处于游客身份，无法访问此功能。请完成实名认证后获得完整服务体验。',
      confirmText: '立即认证',
      cancelText: '稍后再说',
      success(res) {
        if (res.confirm) {
          // 如果用户点击立即认证，则将目标页面保存到本地存储，以便认证完成后跳转
          if (pageToNavigate) {
            wx.setStorageSync('redirectAfterAuth', pageToNavigate)
          }

          // 跳转到认证页面
          wx.navigateTo({
            url: '/pages/auth/real-name/real-name'
          })
        }
      }
    })
    return false
  }
}

// 显示物业人员认证提醒弹窗
const showPropertyAuthModal = (pageToNavigate) => {
  // 检查用户是否已认证为物业人员
  const isPropertyStaff = wx.getStorageSync('isPropertyStaff') || false

  if (isPropertyStaff) {
    // 已认证为物业人员，直接跳转到目标页面
    if (pageToNavigate) {
      wx.navigateTo({
        url: pageToNavigate
      })
    }
    return true
  } else {
    // 未认证为物业人员，显示认证提醒弹窗
    wx.showModal({
      title: '需要物业人员认证',
      content: '此功能仅限物业管理人员使用。请完成物业人员认证后获得访问权限。',
      confirmText: '去认证',
      cancelText: '稍后再说',
      success(res) {
        if (res.confirm) {
          // 如果用户点击去认证，则将目标页面保存到本地存储，以便认证完成后跳转
          if (pageToNavigate) {
            wx.setStorageSync('redirectAfterPropertyAuth', pageToNavigate)
          }

          // 跳转到物业人员认证页面
          wx.navigateTo({
            url: '/pages/auth/property-auth/property-auth'
          })
        }
      }
    })
    return false
  }
}

// 切换暗黑模式
const toggleDarkMode = (isDarkMode) => {
  // 保存设置
  wx.setStorageSync('darkModeEnabled', isDarkMode)

  // 更新全局状态
  const app = getApp()
  app.globalData.darkMode = isDarkMode

  // 应用暗黑模式
  if (isDarkMode) {
    // 设置导航栏颜色
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#1f1f1f',
      animation: {
        duration: 300,
        timingFunc: 'easeIn'
      }
    })

    // 设置标签栏样式
    try {
      wx.setTabBarStyle({
        color: '#8F8F8F',
        selectedColor: '#ffffff',
        backgroundColor: '#1f1f1f',
        borderStyle: 'black'
      })
    } catch (error) {
      console.log('设置标签栏样式失败，可能当前页面没有标签栏：', error)
    }

    // 设置状态栏样式
    wx.setBackgroundColor({
      backgroundColor: '#1c1c1e'
    })

    // 设置下拉背景色
    wx.setBackgroundTextStyle({
      textStyle: 'light'
    })
  } else {
    // 恢复默认样式
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#ff8c00',
      animation: {
        duration: 300,
        timingFunc: 'easeIn'
      }
    })

    // 恢复标签栏样式
    try {
      wx.setTabBarStyle({
        color: '#8F8F8F',
        selectedColor: '#ff8c00',
        backgroundColor: '#ffffff',
        borderStyle: 'white'
      })
    } catch (error) {
      console.log('恢复标签栏样式失败，可能当前页面没有标签栏：', error)
    }

    // 恢复状态栏样式
    wx.setBackgroundColor({
      backgroundColor: '#f8f8f8'
    })

    // 恢复下拉背景色
    wx.setBackgroundTextStyle({
      textStyle: 'dark'
    })
  }

  // 广播暗黑模式状态变化
  try {
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.darkModeChangeEvent = {
        time: new Date().getTime(),
        darkMode: isDarkMode
      };
    }
  } catch (error) {
    console.log('广播暗黑模式状态变化失败：', error);
  }
}

// 检查当前是否为暗黑模式
const isDarkMode = () => {
  return wx.getStorageSync('darkModeEnabled') || false
}

// 应用当前的暗黑模式设置
const applyDarkMode = () => {
  try {
    const isDark = isDarkMode()
    if (isDark) {
      // 设置导航栏颜色
      try {
        wx.setNavigationBarColor({
          frontColor: '#ffffff',
          backgroundColor: '#1f1f1f'
        })
      } catch (error) {
        console.log('设置导航栏颜色失败：', error)
      }

      // 设置标签栏样式
      try {
        wx.setTabBarStyle({
          color: '#8F8F8F',
          selectedColor: '#ffffff',
          backgroundColor: '#1f1f1f',
          borderStyle: 'black'
        })
      } catch (error) {
        console.log('设置标签栏样式失败，可能当前页面没有标签栏：', error)
      }

      // 设置状态栏样式
      try {
        wx.setBackgroundColor({
          backgroundColor: '#1c1c1e'
        })
      } catch (error) {
        console.log('设置状态栏样式失败：', error)
      }

      // 设置下拉背景色
      try {
        wx.setBackgroundTextStyle({
          textStyle: 'light'
        })
      } catch (error) {
        console.log('设置下拉背景色失败：', error)
      }
    }
    return isDark
  } catch (error) {
    console.log('应用暗黑模式设置失败：', error)
    return false
  }
}

// 刷新页面样式
const refreshPageStyle = () => {
  try {
    // 广播样式变化事件
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.styleChangeEvent = {
        time: new Date().getTime()
      };
    }

    return true;
  } catch (error) {
    console.log('刷新页面样式失败：', error);
    return false;
  }
}

/**
 * 计算活动状态
 * @param {string} date 活动日期，格式为 YYYY-MM-DD
 * @param {string} timeRange 活动时间范围，格式为 HH:MM-HH:MM
 * @returns {string} 活动状态：upcoming(即将开始)、ongoing(进行中)、ended(已结束)
 */
const getActivityStatus = (date, timeRange) => {
  const now = new Date()
  const [startTime, endTime] = timeRange.split('-')

  const activityDate = new Date(date)
  const startDateTime = new Date(date)
  const endDateTime = new Date(date)

  const [startHour, startMinute] = startTime.split(':').map(Number)
  const [endHour, endMinute] = endTime.split(':').map(Number)

  startDateTime.setHours(startHour, startMinute, 0, 0)
  endDateTime.setHours(endHour, endMinute, 0, 0)

  if (now < startDateTime) {
    return 'upcoming'
  } else if (now >= startDateTime && now <= endDateTime) {
    return 'ongoing'
  } else {
    return 'ended'
  }
}

/**
 * 获取活动状态的文本表示
 * @param {string} status 活动状态
 * @returns {string} 状态文本
 */
const getActivityStatusText = (status) => {
  const statusMap = {
    'upcoming': '即将开始',
    'ongoing': '进行中',
    'ended': '已结束',
    'full': '报名已满'
  }

  return statusMap[status] || status
}

/**
 * 格式化活动日期
 * @param {string} date 活动日期，格式为 YYYY-MM-DD
 * @returns {string} 格式化后的日期，如 "2023年09月29日"
 */
const formatActivityDate = (date) => {
  const [year, month, day] = date.split('-')
  return `${year}年${month}月${day}日`
}

/**
 * 防抖函数
 * @param {Function} func 要执行的函数
 * @param {number} wait 等待时间（毫秒）
 * @returns {Function} 防抖处理后的函数
 */
const debounce = function (func, wait) {
  let timeout;
  return function () {
    const context = this;
    const args = arguments;
    clearTimeout(timeout);
    timeout = setTimeout(function () {
      func.apply(context, args);
    }, wait);
  };
};


function getDictByNameEn(nameEn) {

  let allDict = wx.getStorageSync('allDict')

  return allDict.filter(item => item.nameEn === nameEn)

}

/**
 * 验证手机号
 * @param {string} phone 手机号
 * @returns {boolean} 是否有效
 */
const validatePhone = function (phone) {
  return /^1\d{10}$/.test(phone);
};

/**
 * 验证身份证号
 * @param {string} idCard 身份证号
 * @returns {boolean} 是否有效
 */
const validateIdCard = function (idCard) {
  return /^\d{17}[\dXx]$/.test(idCard);
};

/**
 * 验证车牌号
 * @param {string} carNumber 车牌号
 * @returns {boolean} 是否有效
 */
const validateCarNumber = function (carNumber) {
  // 普通车牌
  const normalPattern = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{5}$/;
  // 新能源车牌
  const newEnergyPattern = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][DF][A-Z0-9]{5}$/;
  // 大型新能源车牌
  const largeNewEnergyPattern = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{6}$/;

  return normalPattern.test(carNumber) ||
    newEnergyPattern.test(carNumber) ||
    largeNewEnergyPattern.test(carNumber);
};

/**
 * 粘贴手机号到剪贴板
 * @param {Function} callback 成功回调函数，参数为手机号
 * @param {Function} errorCallback 失败回调函数
 */
const pastePhoneNumber = function (callback, errorCallback) {
  wx.getClipboardData({
    success: (res) => {
      const clipboardData = res.data;
      // 检查剪贴板内容是否为手机号
      if (validatePhone(clipboardData)) {
        if (callback) callback(clipboardData);
        wx.showToast({
          title: '已粘贴手机号',
          icon: 'none'
        });
      } else {
        if (errorCallback) errorCallback();
        wx.showToast({
          title: '剪贴板内容不是有效手机号',
          icon: 'none'
        });
      }
    },
    fail: () => {
      if (errorCallback) errorCallback();
      wx.showToast({
        title: '获取剪贴板内容失败',
        icon: 'none'
      });
    }
  });
};

// ==================== 统一状态检测工具类 ====================

/**
 * 检测是否已选择小区
 * @returns {Object} 检测结果 { isSelected: boolean, community: object|null }
 */
const checkCommunitySelected = function () {
  const selectedCommunity = wx.getStorageSync('selectedCommunity');

  if (!selectedCommunity) {
    return {
      isSelected: false,
      community: null,
      message: '请先选择小区'
    };
  }

  // 检查是否为有效的小区对象
  if (typeof selectedCommunity === 'object' && selectedCommunity.id) {
    return {
      isSelected: true,
      community: selectedCommunity,
      message: '已选择小区'
    };
  }

  return {
    isSelected: false,
    community: null,
    message: '小区信息无效，请重新选择'
  };
};

/**
 * 检测用户是否已实名认证
 * @returns {Object} 检测结果 { isAuthenticated: boolean, userInfo: object|null }
 */
const checkUserAuthenticated = function () {
  const userInfo = wx.getStorageSync('userInfo');

  if (!userInfo) {
    return {
      isAuthenticated: false,
      userInfo: null,
      message: '请先实名认证'
    };
  }

  // 检查用户角色和认证状态
  const isAuthenticated = userInfo.role === 'user';

  return {
    isAuthenticated: isAuthenticated,
    userInfo: userInfo,
    message: isAuthenticated ? '已实名认证' : '请完成实名认证'
  };
};

/**
 * 检测用户是否已完成物业员工认证
 * @returns {Object} 检测结果 { isPropertyAuthenticated: boolean, propertyInfo: object|null }
 */
const checkPropertyAuthenticated = function () {
  const propertyAuth = wx.getStorageSync('propertyAuth') || {};
  const propertyInfo = wx.getStorageSync('propertyInfo');
  const isPropertyStaff = wx.getStorageSync('isPropertyStaff') || false;

  // 优先检查新格式的propertyAuth
  if (propertyAuth.isAuthenticated && propertyAuth.status === 'active') {
    return {
      isPropertyAuthenticated: true,
      propertyInfo: propertyAuth,
      message: '已完成物业员工认证'
    };
  }

  // 兼容旧格式检查
  if (!propertyInfo || !isPropertyStaff) {
    return {
      isPropertyAuthenticated: false,
      propertyInfo: null,
      message: '请先完成物业员工认证'
    };
  }

  // 如果有旧格式数据但没有状态信息，认为需要重新验证
  if (!propertyAuth.isAuthenticated) {
    return {
      isPropertyAuthenticated: false,
      propertyInfo: null,
      message: '请重新进行物业员工认证'
    };
  }

  return {
    isPropertyAuthenticated: true,
    propertyInfo: propertyInfo,
    message: '已完成物业员工认证'
  };
};

/**
 * 检测登录状态和小区选择状态
 * @returns {Object} 检测结果 { canProceed: boolean, needLogin: boolean, needCommunity: boolean, needAuth: boolean }
 */
const checkLoginAndCommunity = function () {
  const authResult = checkUserAuthenticated();
  const propertyResult = checkPropertyAuthenticated();
  const communityResult = checkCommunitySelected();

  return {
    canProceed: authResult.isAuthenticated && communityResult.isSelected,
    needLogin: !authResult.userInfo,
    needCommunity: !communityResult.isSelected,
    needAuth: authResult.userInfo && !authResult.isAuthenticated,
    authResult: authResult,
    propertyResult: propertyResult,
    communityResult: communityResult,
    message: !authResult.isAuthenticated ? authResult.message :
      !communityResult.isSelected ? communityResult.message : '检测通过'
  };
};

/**
 * 统一的状态检测和跳转处理
 * @param {string} targetUrl 目标页面URL
 * @param {Object} options 选项 { requireAuth: boolean, requireCommunity: boolean, requireProperty: boolean }
 * @returns {boolean} 是否可以继续执行
 */
const checkStatusAndNavigate = function (targetUrl, options = {}) {
  const { requireAuth = false, requireCommunity = false, requireProperty = false } = options;

  const authResult = checkUserAuthenticated();
  const propertyResult = checkPropertyAuthenticated();
  const communityResult = checkCommunitySelected();

  // 检查登录状态
  if (!authResult.userInfo) {
    wx.showModal({
      title: '需要认证',
      content: '请先认证后再使用此功能',
      showCancel: false,
      confirmText: '确定'
    });
    return false;
  }

  // 检查小区选择
  if (requireCommunity && !communityResult.isSelected) {
    wx.showModal({
      title: '选择小区',
      content: '请先选择您所在的小区',
      confirmText: '去选择',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 保存目标页面，选择小区后跳转
          if (targetUrl) {
            wx.setStorageSync('redirectAfterCommunitySelect', targetUrl);
          }
          wx.navigateTo({
            url: '/pages/community-select/community-select'
          });
        }
      }
    });
    return false;
  }

  // 检查实名认证
  if (requireAuth && !authResult.isAuthenticated) {
    wx.showModal({
      title: '需要实名认证',
      content: '此功能需要完成实名认证后才能使用',
      confirmText: '去认证',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 保存目标页面，认证后跳转
          if (targetUrl) {
            wx.setStorageSync('redirectAfterAuth', targetUrl);
          }
          wx.navigateTo({
            url: '/pages/auth/real-name/real-name?authType=resident&mode=auth'
          });
        }
      }
    });
    return false;
  }

  // 检查物业员工认证
  if (requireProperty) {
    const propertyAuth = wx.getStorageSync('propertyAuth') || {};

    if (!propertyAuth.isAuthenticated) {
      // 未进行物业人员认证
      wx.showModal({
        title: '需要物业员工认证',
        content: '此功能需要完成物业员工认证后才能使用',
        confirmText: '去认证',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 保存目标页面，认证后跳转
            if (targetUrl) {
              wx.setStorageSync('redirectAfterAuth', targetUrl);
            }
            wx.navigateTo({
              url: '/pages/auth/real-name/real-name?authType=property&mode=auth'
            });
          }
        }
      });
      return false;
    }

    // 检查物业人员认证状态是否为active
    if (propertyAuth.status !== 'active') {
      wx.showModal({
        title: '等待审核',
        content: '您的物业人员认证正在审核中，请等待审核通过后使用',
        showCancel: false,
        confirmText: '确定'
      });
      return false;
    }
  }

  // 所有检测通过，可以跳转
  if (targetUrl) {
    wx.navigateTo({
      url: targetUrl
    });
  }

  return true;
};

module.exports = {
  getGreeting,
  getWeatherData,
  getWeatherMood,
  checkAuthentication,
  showAuthModal,
  showPropertyAuthModal,
  toggleDarkMode,
  isDarkMode,
  applyDarkMode,
  refreshPageStyle,
  getActivityStatus,
  getActivityStatusText,
  formatActivityDate,
  debounce,
  getDictByNameEn,
  validatePhone,
  validateIdCard,
  validateCarNumber,
  pastePhoneNumber,
  // 新增的状态检测方法
  checkCommunitySelected,
  checkUserAuthenticated,
  checkPropertyAuthenticated,
  checkLoginAndCommunity,
  checkStatusAndNavigate
}
