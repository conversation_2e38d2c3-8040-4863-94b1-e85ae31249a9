# 聊天页面用户信息获取优化

## 优化概述

通过统一在聊天页面调用 `userApi.getUserInfoByUserId(userId)` 接口获取最新的用户信息，解决了不同页面跳转时头像和用户名不一致的问题。

## 优化方案

### 1. 统一数据获取策略
- **跳转时**：只传递 `targetId`（用户ID）和基本参数
- **聊天页面**：通过 `userApi.getUserInfoByUserId(userId)` 获取最新用户信息
- **头像处理**：在聊天页面统一处理头像URL前缀

### 2. 接口返回数据结构
```json
{
  "id": "38",
  "openid": "o7nun7eStCo5fseNxeSceVHuz-Q0",
  "unionid": null,
  "birthday": null,
  "userName": "我查我自己",
  "avatarUrl": "assets/3b10bb7bb0be47c4aa6af86e2a4a4a71.jpg",
  "gender": null,
  "role": "user"
}
```

## 具体实现

### 1. 聊天页面优化

#### 1.1 添加用户信息获取方法
```javascript
// servicePackage/pages/messages/chat.js
loadUserInfoByUserId: function(userId) {
  console.log('开始获取用户信息，userId:', userId);
  
  wx.showLoading({
    title: '加载用户信息...',
    mask: false
  });

  userApi.getUserInfoByUserId(userId)
    .then(res => {
      wx.hideLoading();
      console.log('获取用户信息成功:', res);
      
      if (res) {
        // 处理头像URL
        const processedAvatar = this.processAvatarUrl(res.avatarUrl);
        
        // 更新页面数据
        this.setData({
          targetName: res.userName || '对方',
          targetAvatar: processedAvatar
        });

        // 更新导航栏标题
        wx.setNavigationBarTitle({
          title: res.userName || '聊天'
        });
      }
    })
    .catch(err => {
      wx.hideLoading();
      console.error('获取用户信息失败:', err);
      // 获取失败时不影响聊天功能，继续使用传入的参数
    });
}
```

#### 1.2 在onLoad中调用
```javascript
onLoad: function (options) {
  // ... 其他初始化代码
  
  // 通过userId获取最新的用户信息
  if (finalTargetId) {
    this.loadUserInfoByUserId(finalTargetId);
  }
}
```

### 2. 各页面跳转优化

#### 2.1 好物详情页
```javascript
// profilePackage/pages/goods/detail/detail.js
contactSeller: function() {
  const goods = this.data.goods;
  const sellerId = goods.userId || '';
  const sellerName = goods.userName || '卖家';
  
  // 不传递头像，让聊天页面通过接口获取
  wx.navigateTo({
    url: `/servicePackage/pages/messages/chat?targetId=${sellerId}&targetName=${sellerName}&goodsId=${goodsId}&goodsTitle=${goodsTitle}&goodsImage=${goodsImage}`
  });
}
```

#### 2.2 订单详情页
```javascript
// profilePackage/pages/goods/order/order.js
contactSeller: function () {
  const order = this.data.order;
  
  // 不传递头像，让聊天页面通过接口获取
  wx.navigateTo({
    url: `/servicePackage/pages/messages/chat?targetId=${order.sellerId}&targetName=${order.sellerName}&goodsId=${order.goodsId}&goodsTitle=${order.stuffDescribe}&goodsImage=${order.image}`
  });
}
```

#### 2.3 我的订单页面（重点优化）
```javascript
// profilePackage/pages/goods/my/my.js
contactSeller: function (e) {
  const order = e.currentTarget.dataset.order;
  
  // 优先从stuffSnapshot（交易快照）中获取userId
  let sellerId = '';
  let sellerName = '卖家';
  
  if (order.stuffSnapshot && order.stuffSnapshot.userId) {
    // 从交易快照中获取卖家信息
    sellerId = order.stuffSnapshot.userId;
    sellerName = order.stuffSnapshot.userName || '卖家';
    console.log('从交易快照中获取卖家信息:', {
      userId: sellerId,
      userName: sellerName
    });
  } else {
    // 兜底：从订单数据中获取
    sellerId = order.sellerId || order.userId || '';
    sellerName = order.sellerName || order.userName || '卖家';
    console.log('从订单数据中获取卖家信息:', {
      userId: sellerId,
      userName: sellerName
    });
  }
  
  // 不传递头像，让聊天页面通过接口获取
  wx.navigateTo({
    url: `/servicePackage/pages/messages/chat?targetId=${sellerId}&targetName=${sellerName}&goodsId=${goodsId}&goodsTitle=${goodsTitle}&goodsImage=${goodsImage}`
  });
}
```

#### 2.4 站内私信列表
```javascript
// pages/messages/messages.js
handlePrivateMessageTap: function (e) {
  const message = e.currentTarget.dataset.message;
  const userId = message.userId || message.id;
  const userName = message.userName || message.title || '未知用户';
  
  // 不传递头像，让聊天页面通过接口获取
  wx.navigateTo({
    url: `/servicePackage/pages/messages/chat?targetId=${userId}&targetName=${userName}`
  });
}
```

## 优化效果

### 1. 数据一致性
- **统一数据源**：所有用户信息都来自同一个接口
- **实时更新**：获取的是最新的用户信息，包括头像和用户名
- **避免缓存问题**：不依赖页面间传递的可能过期的数据

### 2. 代码简化
- **减少参数传递**：不需要在各个页面间传递头像URL
- **统一处理逻辑**：头像URL处理逻辑集中在聊天页面
- **降低维护成本**：只需要维护一套用户信息获取逻辑

### 3. 用户体验
- **加载提示**：显示"加载用户信息..."提示
- **容错处理**：接口失败时不影响聊天功能
- **实时标题**：导航栏标题显示最新的用户名

## 数据来源说明

### 1. 好物详情页
- **数据来源**：好物详情API返回的卖家信息
- **userId字段**：`goods.userId`
- **用户名字段**：`goods.userName`

### 2. 订单详情页
- **数据来源**：订单详情API + 商品快照
- **userId字段**：`order.sellerId` 或 `goodsInfo.userId`
- **用户名字段**：`goodsInfo.userName`

### 3. 我的订单页面
- **数据来源**：订单列表API + 交易快照（stuffSnapshot）
- **userId字段**：优先从 `order.stuffSnapshot.userId` 获取，兜底使用 `order.sellerId`
- **用户名字段**：优先从 `order.stuffSnapshot.userName` 获取

### 4. 站内私信列表
- **数据来源**：私信列表API
- **userId字段**：`message.userId`
- **用户名字段**：`message.userName`

## 头像URL处理

### 1. 处理逻辑
```javascript
processAvatarUrl: function(avatarPath) {
  // 如果没有头像路径，返回默认头像
  if (!avatarPath || avatarPath.trim() === '') {
    return '/images/default-avatar.svg';
  }

  // 如果已经是完整的HTTP/HTTPS URL，直接返回
  if (avatarPath.startsWith('http://') || avatarPath.startsWith('https://')) {
    return avatarPath;
  }

  // 如果是相对路径，添加API前缀
  const apiUrl = wx.getStorageSync('apiUrl') || '';
  if (apiUrl) {
    let processedPath = avatarPath.trim();
    if (!processedPath.startsWith('/')) {
      processedPath = '/' + processedPath;
    }
    
    // 构建完整的头像URL
    if (processedPath.includes('/common-api/v1/file/')) {
      return apiUrl + processedPath;
    } else {
      return apiUrl + '/common-api/v1/file' + processedPath;
    }
  }

  // 如果没有API URL，返回默认头像
  return '/images/default-avatar.svg';
}
```

### 2. 默认头像
- **路径**：`/images/default-avatar.svg`
- **使用场景**：用户没有设置头像或头像加载失败时

## 修改的文件

1. **`servicePackage/pages/messages/chat.js`** - 添加用户信息获取逻辑
2. **`profilePackage/pages/goods/detail/detail.js`** - 移除头像参数传递
3. **`profilePackage/pages/goods/order/order.js`** - 移除头像参数传递
4. **`profilePackage/pages/goods/my/my.js`** - 优化userId获取逻辑，移除头像参数传递
5. **`pages/messages/messages.js`** - 移除头像参数传递

## 注意事项

1. **网络异常处理**：接口调用失败时不影响聊天功能
2. **加载性能**：接口调用时显示加载提示，提升用户体验
3. **数据兜底**：我的订单页面优先从交易快照获取userId，确保数据准确性
4. **头像缓存**：微信小程序会自动缓存图片，无需额外处理

通过这次优化，聊天页面的用户信息显示更加准确和一致，用户体验得到显著提升。
