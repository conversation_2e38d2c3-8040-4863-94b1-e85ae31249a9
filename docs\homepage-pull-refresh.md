# 首页下拉刷新功能

## 功能概述

为首页添加了下拉刷新功能，用户可以通过下拉手势刷新页面数据，包括：
- 用户信息
- 物业员工认证信息  
- 轮播图数据
- 未读消息数量

## 实现方案

### 1. 页面配置

在 `pages/index/index.json` 中启用下拉刷新：

```json
{
  "usingComponents": {},
  "enablePullDownRefresh": true,
  "backgroundTextStyle": "dark"
}
```

### 2. 核心方法

#### 2.1 下拉刷新入口方法

```javascript
onPullDownRefresh: function () {
  console.log('=== 开始下拉刷新 ===');
  
  // 显示刷新提示
  wx.showToast({
    title: '刷新中...',
    icon: 'loading',
    duration: 1000,
    mask: true
  });

  // 执行刷新操作
  this.refreshPageData()
    .then(() => {
      console.log('✅ 下拉刷新完成');
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      });
    })
    .catch(err => {
      console.error('❌ 下拉刷新失败:', err);
      wx.showToast({
        title: '刷新失败',
        icon: 'none',
        duration: 2000
      });
    })
    .finally(() => {
      // 停止下拉刷新动画
      wx.stopPullDownRefresh();
    });
}
```

#### 2.2 刷新页面数据协调方法

```javascript
refreshPageData: function () {
  return new Promise((resolve, reject) => {
    console.log('开始刷新页面数据...');
    
    // 重置加载状态
    this.setData({
      bannerLoading: false,
      bannerLoaded: false
    });

    // 并行执行多个刷新任务
    const refreshTasks = [
      this.refreshUserInfo(),
      this.refreshPropertyAuth(),
      this.refreshBannerData(),
      this.refreshUnreadCount()
    ];

    Promise.allSettled(refreshTasks)
      .then(results => {
        // 检查刷新结果
        const failedTasks = results.filter(result => result.status === 'rejected');
        
        if (failedTasks.length > 0) {
          console.warn('部分刷新任务失败:', failedTasks);
          // 即使部分失败也认为刷新成功，只要有任务成功
          if (results.some(result => result.status === 'fulfilled')) {
            resolve();
          } else {
            reject(new Error('所有刷新任务都失败了'));
          }
        } else {
          console.log('所有刷新任务都成功完成');
          resolve();
        }
      })
      .catch(err => {
        console.error('刷新任务执行异常:', err);
        reject(err);
      });
  });
}
```

### 3. 具体刷新方法

#### 3.1 刷新用户信息

```javascript
refreshUserInfo: function () {
  return new Promise((resolve, reject) => {
    console.log('刷新用户信息...');
    
    userApi.getUserInfo()
      .then(res => {
        if (res) {
          console.log('✅ 用户信息刷新成功:', res);
          
          // 更新用户信息
          wx.setStorageSync('userInfo', res);
          
          // 更新页面状态
          this.setData({
            isAuthenticated: res.role === 'user',
            userName: res.realName || res.nickName || '用户'
          });
          
          resolve(res);
        } else {
          console.log('用户信息为空，可能是游客状态');
          this.setData({
            isAuthenticated: false,
            userName: '游客'
          });
          resolve(null);
        }
      })
      .catch(err => {
        console.error('❌ 刷新用户信息失败:', err);
        reject(err);
      });
  });
}
```

#### 3.2 刷新物业员工认证信息

```javascript
refreshPropertyAuth: function () {
  return new Promise((resolve, reject) => {
    console.log('刷新物业员工认证信息...');
    
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    const communityId = selectedCommunity && selectedCommunity.id;
    
    if (!communityId) {
      console.log('没有选择小区，跳过物业认证刷新');
      resolve();
      return;
    }

    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || userInfo.role === 'tourist') {
      console.log('用户为游客，跳过物业认证刷新');
      resolve();
      return;
    }

    userApi.getProperTyInfo(communityId)
      .then(res => {
        console.log('✅ 物业员工认证信息刷新成功:', res);
        
        if (res) {
          // 保存物业员工认证信息
          wx.setStorageSync('propertyInfo', res);
          wx.setStorageSync('isPropertyStaff', true);

          // 保存新格式的物业认证信息
          const propertyAuth = {
            isAuthenticated: true,
            status: res.status || 'pending',
            ...res
          };
          wx.setStorageSync('propertyAuth', propertyAuth);
          
          console.log('物业员工认证信息已更新:', propertyAuth);
        } else {
          // 清除物业认证信息
          wx.removeStorageSync('propertyInfo');
          wx.removeStorageSync('propertyAuth');
          wx.setStorageSync('isPropertyStaff', false);
          console.log('用户不是物业员工');
        }
        
        resolve(res);
      })
      .catch(err => {
        console.error('❌ 刷新物业认证信息失败:', err);
        reject(err);
      });
  });
}
```

#### 3.3 刷新轮播图数据

```javascript
refreshBannerData: function () {
  return new Promise((resolve, reject) => {
    console.log('刷新轮播图数据...');
    
    // 重置轮播图状态
    this.setData({
      bannerLoading: true,
      bannerLoaded: false
    });

    imagetextApi.getBannerList({
      pageNum: 1,
      pageSize: 10
    })
      .then(res => {
        console.log('✅ 轮播图数据刷新成功:', res);
        
        if (res && res.list && res.list.length > 0) {
          // 处理轮播图数据
          const bannerList = res.list.map(item => ({
            id: item.id,
            title: item.title || '',
            imageUrl: this.data.apiUrl + '/common-api/v1/file/' + item.imageUrl,
            linkUrl: item.link || '',
            sort: item.sort || 0
          }));

          // 按排序字段排序
          bannerList.sort((a, b) => (a.sort || 0) - (b.sort || 0));

          this.setData({
            bannerList: bannerList,
            bannerLoading: false,
            bannerLoaded: true
          });
        } else {
          // 如果API返回空数据
          this.setData({
            bannerList: [],
            bannerLoading: false,
            bannerLoaded: true
          });
        }
        
        resolve(res);
      })
      .catch(err => {
        console.error('❌ 刷新轮播图数据失败:', err);
        
        // 重置加载状态
        this.setData({
          bannerLoading: false,
          bannerLoaded: true
        });
        
        reject(err);
      });
  });
}
```

#### 3.4 刷新未读消息数量

```javascript
refreshUnreadCount: function () {
  return new Promise((resolve, reject) => {
    console.log('刷新未读消息数量...');
    
    // 这里可以调用获取未读消息数量的API
    // 暂时使用现有的方法
    try {
      this.loadTotalUnreadCount();
      console.log('✅ 未读消息数量刷新完成');
      resolve();
    } catch (err) {
      console.error('❌ 刷新未读消息数量失败:', err);
      reject(err);
    }
  });
}
```

## 功能特点

### 1. 并行刷新
- 使用 `Promise.allSettled()` 并行执行多个刷新任务
- 提高刷新效率，减少用户等待时间

### 2. 容错处理
- 即使部分刷新任务失败，只要有任务成功就认为刷新成功
- 提供详细的错误日志，便于调试

### 3. 用户体验
- 显示刷新进度提示
- 刷新完成后显示成功或失败消息
- 自动停止下拉刷新动画

### 4. 状态管理
- 正确重置加载状态
- 更新本地存储数据
- 同步页面显示状态

## 使用方法

用户在首页向下拉动页面即可触发刷新功能，系统会自动：

1. 显示"刷新中..."提示
2. 并行刷新所有数据
3. 显示刷新结果
4. 停止下拉刷新动画

## 注意事项

1. 刷新过程中会重置轮播图的加载状态
2. 物业认证信息只有在已选择小区且非游客状态下才会刷新
3. 所有刷新操作都会更新本地存储中的数据
4. 刷新失败不会影响页面的正常使用
