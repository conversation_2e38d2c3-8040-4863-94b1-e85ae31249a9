// pages/workorder/detail/index.js
const workOrderApi = require('@/api/workOrderApi.js')
const util = require('@/utils/util.js')

Page({
  data: {
    workOrder: null,
    loading: true,
    showCancelConfirm: false,
    workOrderStatus: [],
    workOrderType: [],
    apiUrl: ''
  },

  onLoad(options) {
    const { id } = options

    // 初始化API地址
    this.setData({
      apiUrl: wx.getStorageSync('apiUrl')
    })

    if (id) {
      this.getWorkOrderStatusDict()
      this.getWorkOrderTypeDict()
      this.loadWorkOrderDetail(id)
    } else {
      wx.showToast({
        title: '工单ID不存在',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 获取工单状态字典
  getWorkOrderStatusDict: function () {
    var workOrderStatus = util.getDictByNameEn('work_order_status')[0].children
    this.setData({
      workOrderStatus
    })
  },

  // 获取工单类型字典
  getWorkOrderTypeDict: function () {
    var workOrderType = util.getDictByNameEn('work_order_type')[0].children
    console.log('工单类型字典数据:', workOrderType)
    // 打印每个类型的详细信息
    workOrderType.forEach((type, index) => {
      console.log(`类型${index}:`, type.nameEn, '->', type.nameCn)
    })
    this.setData({
      workOrderType
    })
  },

  // 获取工单紧急程度字典
  getWorkOrderLevelDict: function () {
    try {
      var workOrderLevel = util.getDictByNameEn('work_order_level')[0].children
      console.log('工单紧急程度字典数据:', workOrderLevel)
      this.setData({
        workOrderLevel
      })
    } catch (error) {
      console.error('获取工单紧急程度字典失败:', error)
      // 设置默认值
      this.setData({
        workOrderLevel: [
          { nameEn: 'low', nameCn: '低' },
          { nameEn: 'medium', nameCn: '中' },
          { nameEn: 'high', nameCn: '高' },
          { nameEn: 'urgent', nameCn: '紧急' }
        ]
      })
    }
  },

  // 加载工单详情
  loadWorkOrderDetail(id) {
    this.setData({ loading: true })

    // 调用API获取工单详情
    workOrderApi.getWorkOrderDetail(id)
      .then(response => {
        const order = response
         
        console.log('工单详情数据:', order)

        // 处理工单数据
        const processedOrder = {
          ...order,
          // 处理图片字段 - 支持单个图片或逗号分隔的多个图片
          imageList: order.media ? order.media.split(',').filter(img => img.trim()) : [],
          // 获取状态显示名称
          statusName: this.getStatusName(order.status),
          // 获取类型显示名称
          typeName: this.getTypeName(order.type),
          // 获取区域类型显示名称
          regionTypeName: this.getRegionTypeName(order.regionType),
          // 格式化时间
          createTimeFormatted: this.formatTime(order.createTime),
          updateTimeFormatted: order.updateTime ? this.formatTime(order.updateTime) : '',
          // 处理报修人信息
          reporterInfo: {
            name: order.residentName || '未知',
            phone: order.residentPhone || '',
            address: order.residentAddress || ''
          },
          // 处理时间线数据
          processedTimeLine: this.processTimeLine(order.timeLine || [])
        }

        this.setData({
          workOrder: processedOrder,
          loading: false
        })
      })
      .catch(error => {
        console.error('加载工单详情失败', error)
        this.setData({ loading: false })
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        })
      })
  },

  // 获取状态显示名称
  getStatusName: function (statusEn) {
    const status = this.data.workOrderStatus.find(item => item.nameEn === statusEn)
    return status ? status.nameCn : statusEn
  },

  // 获取类型显示名称
  getTypeName: function (typeEn) {
    console.log('查找类型:', typeEn)
    console.log('可用类型列表:', this.data.workOrderType)

    // 先尝试精确匹配
    let type = this.data.workOrderType.find(item => {
      console.log('比较:', item.nameEn, '===', typeEn, '结果:', item.nameEn === typeEn)
      return item.nameEn === typeEn
    })

    // 如果精确匹配失败，尝试去除特殊字符后匹配
    if (!type) {
      console.log('精确匹配失败，尝试清理字符后匹配')
      type = this.data.workOrderType.find(item => {
        // 清理可能的特殊字符
        const cleanNameEn = item.nameEn.replace(/[\u200B-\u200D\uFEFF]/g, '').trim()
        const cleanTypeEn = typeEn.replace(/[\u200B-\u200D\uFEFF]/g, '').trim()
        console.log('清理后比较:', cleanNameEn, '===', cleanTypeEn, '结果:', cleanNameEn === cleanTypeEn)
        return cleanNameEn === cleanTypeEn
      })
    }

    // 如果还是找不到，使用备用映射
    if (!type) {
      console.log('字典匹配失败，使用备用映射')
      const fallbackMap = {
        'repair': '维修',
        'complaint': '投诉',
        'suggestion': '建议',
        'other': '其他'
      }
      return fallbackMap[typeEn] || typeEn
    }

    console.log('找到的类型:', type)
    return type ? type.nameCn : typeEn
  },

  // 获取区域类型显示名称
  getRegionTypeName: function (regionTypeEn) {
    // 区域类型映射
    const regionTypeMap = {
      'house': '房屋',
      'public_area': '公共区域'
    }
    return regionTypeMap[regionTypeEn] || regionTypeEn
  },

  // 格式化时间
  formatTime: function (timeStr) {
    if (!timeStr) return ''
    const date = new Date(timeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  },

  // 处理时间线数据
  processTimeLine: function(timeLine) {
    if (!timeLine || !Array.isArray(timeLine)) return [];

    return timeLine.map(item => {
      // 获取操作员名称
      let operatorName = '系统';
      if (item.personName) {
        operatorName = item.personName;
      } else if (item.residentName) {
        operatorName = item.residentName;
      }

      // 获取状态显示文本
      const statusText = this.getStatusName(item.status) || '状态变更';

      // 处理图片字段 - 支持单个图片或逗号分隔的多个图片
      const imageList = item.media ? item.media.split(',').filter(img => img.trim()) : [];

      return {
        ...item,
        operatorName: operatorName,
        statusText: statusText,
        formattedTime: this.formatTimelineTime(item.createTime),
        imageList: imageList
      };
    }).sort((a, b) => new Date(b.createTime) - new Date(a.createTime)); // 按时间倒序排列
  },

  // 格式化时间线时间显示
  formatTimelineTime: function(timeStr) {
    if (!timeStr) return '';

    const date = new Date(timeStr);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const itemDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    const diffDays = Math.floor((today - itemDate) / (1000 * 60 * 60 * 24));

    const timeFormat = date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });

    if (diffDays === 0) {
      return `今天 ${timeFormat}`;
    } else if (diffDays === 1) {
      return `昨天 ${timeFormat}`;
    } else if (diffDays < 7) {
      return `${diffDays}天前 ${timeFormat}`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  },

  // 显示取消确认对话框
  showCancelConfirm() {
    this.setData({ showCancelConfirm: true })
  },

  // 隐藏取消确认对话框
  hideCancelConfirm() {
    this.setData({ showCancelConfirm: false })
  },

  // 取消工单
  cancelOrder() {
    const { workOrder } = this.data

    // 调用API取消工单
    workOrderApi.cancelWorkOrder(workOrder.id)
      .then(res => {

    
          this.hideCancelConfirm()

          // 设置刷新标记，确保返回列表页时刷新
          wx.setStorageSync('workOrderListNeedRefresh', true)

          // 重新加载工单详情
          this.loadWorkOrderDetail(workOrder.id)

          wx.showToast({
            title: '工单已取消',
            icon: 'success'
          })
     

      })
      .catch(error => {
        console.error('取消工单失败', error)
        this.hideCancelConfirm()

        wx.showToast({
          title: error.errorMessage || '操作失败，请重试',
          icon: 'none'
        })
      })
  },

  // 导航到评价页面
  navigateToEvaluate() {
    const { workOrder } = this.data
    wx.navigateTo({
      url: `/servicePackage/pages/workorder/evaluate/index?id=${workOrder.id}`
    })
  },

  // 开始处理工单（从挂起状态恢复）
  startProcessing() {
    const { workOrder } = this.data

    wx.showLoading({
      title: '处理中...',
      mask: true
    })

    // 调用API更新工单状态为处理中
    workOrderApi.updateWorkOrderStatus(workOrder.id, 'processing')
      .then(res => {
        // 设置刷新标记，确保返回列表页时刷新
        wx.setStorageSync('workOrderListNeedRefresh', true)

        // 重新加载工单详情
        this.loadWorkOrderDetail(workOrder.id)

        wx.hideLoading()
        wx.showToast({
          title: '已开始处理',
          icon: 'success'
        })
      })
      .catch(error => {
        wx.hideLoading()
        console.error('开始处理失败', error)

        wx.showToast({
          title: error.errorMessage || '操作失败，请重试',
          icon: 'none'
        })
      })
  },

  // 预览图片
  previewImage(e) {
    const { index } = e.currentTarget.dataset
    const { workOrder, apiUrl } = this.data

    if (workOrder.imageList && workOrder.imageList.length > 0) {
      // 处理图片URL，支持完整URL和相对路径
      const fullUrls = workOrder.imageList.map(img => {
        if (img.startsWith('http')) {
          return img // 已经是完整URL
        } else {
          return apiUrl + '/common-api/v1/file/' + img
        }
      })

      wx.previewImage({
        current: fullUrls[index],
        urls: fullUrls
      })
    }
  },

  // 预览评价图片
  previewEvaluationImage(e) {
    const { index } = e.currentTarget.dataset
    const { workOrder } = this.data

    if (workOrder.evaluation && workOrder.evaluation.images && workOrder.evaluation.images.length > 0) {
      wx.previewImage({
        current: workOrder.evaluation.images[index],
        urls: workOrder.evaluation.images
      })
    }
  },

  // 预览时间线图片
  previewTimelineImage(e) {
    const { index, images } = e.currentTarget.dataset
    const { apiUrl } = this.data

    // 构建完整的图片URL数组
    const urls = images.map(img => {
      return img.startsWith('http') ? img : `${apiUrl}/common-api/v1/file/${img}`
    })

    wx.previewImage({
      current: urls[index],
      urls: urls
    })
  }

})
